# events/views.py
import logging
import datetime
from django.db.models import Q
from django.db import transaction
from django.core.files.base import ContentFile
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from io import BytesIO
from django.utils import timezone
from datetime import timedelta  # *** ADD THIS LINE ***


# NEW: Import the enterprise queue service
from facial_recognition.queue_service import add_photos_to_queue, get_queue_status

from .models import Event, EventAttendance, EventPhoto
from .serializers import (
    EventSerializer, EventAttendanceSerializer,
    EventJoinSerializer, NearbyEventsRequestSerializer,
    EventListSerializer, EventCreateSerializer,
    EventSearchSerializer, EventPhotoSerializer,
    EventPhotoUploadSerializer, BatchEventPhotoUploadSerializer,
    PhotographerEventListSerializer
)
from .utils import compress_image, process_event_photos
from .permissions import (IsEventOwnerOrReadOn<PERSON>, IsEventCreator<PERSON><PERSON><PERSON><PERSON><PERSON>, 
                          CanJoinLeaveEvent, CanManagePhotographerRe<PERSON><PERSON>, 
                          CanJoinAsPhotographer, IsApprovedPhotographerOrCreator)

from django.contrib.auth import get_user_model
from .models import EventUserRole, Notification
from .serializers import PhotographerRequestSerializer, JoinAsPhotographerSerializer

User = get_user_model()
logger = logging.getLogger('events')


# Add these validation functions to the file
def validate_upload_timing(event):
    """
    Validate if photo upload is allowed based on event timing.
    """
    from django.utils import timezone
    from datetime import timedelta
    
    now = timezone.now().date()
    
    # If event doesn't have start_date, allow uploads (backward compatibility)
    if not event.start_date:
        return True, None
    
    # Check if event hasn't started yet
    if now < event.start_date:
        return False, Response({
            'error': 'Photo upload not allowed',
            'detail': f'Event "{event.name}" has not started yet. Upload will be available from {event.start_date}.',
            'event_start_date': event.start_date.isoformat(),
            'current_date': now.isoformat(),
            'upload_status': 'NOT_STARTED'
        }, status=status.HTTP_403_FORBIDDEN)
    
    # If event has end_date, check buffer period
    if event.end_date:
        # Calculate buffer end date (2 days after event ends)
        buffer_end_date = event.end_date + timedelta(days=2)
        
        # Check if we're past the buffer period
        if now > buffer_end_date:
            return False, Response({
                'error': 'Photo upload period has ended',
                'detail': f'Event "{event.name}" ended on {event.end_date}. Upload deadline was {buffer_end_date}.',
                'event_end_date': event.end_date.isoformat(),
                'upload_deadline': buffer_end_date.isoformat(),
                'current_date': now.isoformat(),
                'upload_status': 'DEADLINE_PASSED'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Check if we're in the buffer period (after event ended)
        if now > event.end_date:
            days_remaining = (buffer_end_date - now).days
            return True, {
                'warning': 'Upload deadline approaching',
                'detail': f'Event has ended. You have {days_remaining} day(s) remaining to upload photos.',
                'upload_deadline': buffer_end_date.isoformat(),
                'upload_status': 'BUFFER_PERIOD'
            }
    
    # Event is ongoing or hasn't ended yet
    return True, {
        'upload_status': 'ACTIVE',
        'detail': 'Photo upload is allowed'
    }


def get_upload_status_info(event):
    """
    Get detailed upload status information for an event.
    """
    from django.utils import timezone
    from datetime import timedelta
    
    now = timezone.now().date()
    
    if not event.start_date:
        return {
            'upload_allowed': True,
            'status': 'ALWAYS_ALLOWED',
            'message': 'No date restrictions set for this event'
        }
    
    # Event hasn't started
    if now < event.start_date:
        days_until_start = (event.start_date - now).days
        return {
            'upload_allowed': False,
            'status': 'NOT_STARTED',
            'message': f'Upload will be available in {days_until_start} day(s)',
            'event_start_date': event.start_date.isoformat(),
            'days_until_start': days_until_start
        }
    
    # Event is ongoing (started but not ended, or no end date)
    if not event.end_date or now <= event.end_date:
        return {
            'upload_allowed': True,
            'status': 'ACTIVE',
            'message': 'Event is active - uploads allowed',
            'event_start_date': event.start_date.isoformat(),
            'event_end_date': event.end_date.isoformat() if event.end_date else None
        }
    
    # Event has ended - check buffer period
    buffer_end_date = event.end_date + timedelta(days=2)
    
    # Still in buffer period
    if now <= buffer_end_date:
        days_remaining = (buffer_end_date - now).days
        return {
            'upload_allowed': True,
            'status': 'BUFFER_PERIOD',
            'message': f'Event ended - {days_remaining} day(s) remaining to upload',
            'event_end_date': event.end_date.isoformat(),
            'upload_deadline': buffer_end_date.isoformat(),
            'days_remaining': days_remaining
        }
    
    # Buffer period has ended
    return {
        'upload_allowed': False,
        'status': 'DEADLINE_PASSED',
        'message': 'Upload deadline has passed',
        'event_end_date': event.end_date.isoformat(),
        'upload_deadline': buffer_end_date.isoformat()
    }


class EventViewSet(viewsets.ModelViewSet):
    queryset = Event.objects.all().order_by('-start_date')
    serializer_class = EventSerializer
    permission_classes = [IsAuthenticated, IsEventOwnerOrReadOnly]
    filter_backends = [filters.SearchFilter]
    search_fields = ['name', 'description', 'location', 'event_type']

    def get_permissions(self):
        """Return different permissions based on action with invitation system support"""
        if self.action in ['list', 'retrieve', 'search', 'nearby', 'attended_events', 
                        'created_events', 'home_events', 'event_details', 'photos']:
            return [IsAuthenticated()]
        elif self.action in ['join_event', 'leave_event']:
            return [IsAuthenticated(), CanJoinLeaveEvent()]
        elif self.action in ['upload_photo', 'batch_upload_photos']:
            # *** UPDATED: Now checks for APPROVED photographer status ***
            return [IsAuthenticated(), IsEventCreatorOrPhotographer()]
        elif self.action == 'join_as_photographer':
            # *** NEW: Permission for joining as photographer ***
            return [IsAuthenticated(), CanJoinAsPhotographer()]
        elif self.action in ['photographer_requests', 'approve_photographer_request', 'deny_photographer_request']:
            # *** NEW: Permission for managing photographer requests ***
            return [IsAuthenticated(), CanManagePhotographerRequests()]
        elif self.action in ['add_photographer', 'remove_photographer']:
            # *** EXISTING: Event creator permissions ***
            return [IsAuthenticated(), IsEventOwnerOrReadOnly()]
        else:
            return [IsAuthenticated(), IsEventOwnerOrReadOnly()]

    def get_serializer_class(self):
        """Return different serializers based on action"""
        if self.action == 'list':
            return EventListSerializer
        elif self.action == 'create':
            return EventCreateSerializer
        elif self.action == 'upload_photos':
            return EventPhotoUploadSerializer
        elif self.action == 'batch_upload_photos':
            return BatchEventPhotoUploadSerializer
        elif self.action == 'photos':
            return EventPhotoSerializer
        return EventSerializer

    def perform_create(self, serializer):
        """Create new event and set creator"""
        event = serializer.save(creator=self.request.user)
        
        # Set jersey number requirement based on event type
        if event.event_type == Event.EventType.SPORTS:
            event.requires_jersey_number = True
            event.save(update_fields=['requires_jersey_number'])
            
        logger.info(f"Event created: {event.id} by user {self.request.user.id}")

    def get_queryset(self):
        """Enhanced filtering logic for list action with user-specific filtering"""
        queryset = Event.objects.all().order_by('-start_date')
        
        # Special filtering for list action (GET /api/v1/events/)
        if self.action == 'list':
            return self._get_filtered_events_for_list()
        
        # For public search, exclude private events
        if self.action == 'search':
            queryset = queryset.filter(visibility=Event.EventVisibility.PUBLIC)
        
        # Filter by location if provided
        user_location = self.request.query_params.get('location')
        if user_location:
            queryset = queryset.filter(location__icontains=user_location)
            
        # Get attended events only
        if self.action == 'attended_events':
            user = self.request.user
            attended_event_ids = EventAttendance.objects.filter(
                user=user, is_attending=True
            ).values_list('event_id', flat=True)
            return queryset.filter(id__in=attended_event_ids)
            
        # Get created events only
        if self.action == 'created_events':
            return queryset.filter(creator=self.request.user)
            
        # Home screen events query - both attended and created
        if self.action == 'home_events':
            user = self.request.user
            attended_event_ids = EventAttendance.objects.filter(
                user=user, is_attending=True
            ).values_list('event_id', flat=True)
            return queryset.filter(
                Q(id__in=attended_event_ids) | Q(creator=user)
            )
            
        return queryset

    @action(detail=True, methods=['get'])
    def event_details(self, request, pk=None):
        """Get detailed event information with role-based navigation"""
        event = self.get_object()
        user = request.user
        
        # Determine user's role and permissions
        is_creator = event.creator == user
        is_photographer = event.photographers.filter(id=user.id).exists()
        is_attendee = EventAttendance.objects.filter(
            event=event, user=user, is_attending=True
        ).exists()
        
        # Determine navigation action
        if is_creator:
            navigation_action = 'SHOW_MANAGEMENT_SCREEN'
            user_role = 'CREATOR'
        elif is_photographer:
            navigation_action = 'SHOW_PHOTOGRAPHER_SCREEN'
            user_role = 'PHOTOGRAPHER'  
        elif is_attendee:
            navigation_action = 'SHOW_ATTENDEE_SCREEN'
            user_role = 'ATTENDEE'
        elif event.visibility == Event.EventVisibility.PUBLIC:
            navigation_action = 'SHOW_JOIN_PROMPT'
            user_role = 'NON_ATTENDEE'
        else:
            return Response({
                'error': 'Permission denied - This is a private event'
            }, status=status.HTTP_403_FORBIDDEN)
        
        # Serialize event data safely
        try:
            serializer = self.get_serializer(event)
            event_data = serializer.data.copy()
        except Exception as e:
            # If serialization fails, create minimal event data
            event_data = {
                'id': str(event.id),
                'name': event.name,
                'description': event.description,
                'start_date': event.start_date,
                'end_date': event.end_date,
                'location': event.location,
                'event_type': event.event_type,
                'visibility': event.visibility,
            }
        
        # Add navigation context
        event_data['navigation'] = {
            'action': navigation_action,
            'user_role': user_role,
            'permissions': {
                'can_manage': is_creator,
                'can_upload': is_creator or is_photographer,
                'can_view_photos': True,
                'can_delete_event': is_creator
            }
        }
        
        return Response(event_data)

    @action(detail=False, methods=['get'])
    def attended_events(self, request):
        """Get events the current user is attending"""
        events = self.get_queryset()
        serializer = self.get_serializer(events, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def created_events(self, request):
        """Get events created by the current user"""
        events = self.get_queryset()
        serializer = self.get_serializer(events, many=True)
        return Response(serializer.data)
        
    @action(detail=False, methods=['get'])
    def home_events(self, request):
        """Get both attended and created events for home screen"""
        events = self.get_queryset()
        serializer = self.get_serializer(events, many=True)
        return Response(serializer.data)
        
    @action(detail=False, methods=['post'])
    def search(self, request):
        """Search for public events"""
        serializer = EventSearchSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
        query = serializer.validated_data.get('query', '')
        event_type = serializer.validated_data.get('event_type', '')
        location = serializer.validated_data.get('location', '')
        date = serializer.validated_data.get('date')
        
        # Start with public events only
        events = Event.objects.filter(visibility=Event.EventVisibility.PUBLIC)
        
        # Apply filters
        if query:
            events = events.filter(Q(name__icontains=query) | Q(description__icontains=query))
        if event_type:
            events = events.filter(event_type=event_type)
        if location:
            events = events.filter(location__icontains=location)
        if date:
            events = events.filter(Q(start_date=date) | Q(end_date=date) | 
                                  Q(start_date__lte=date, end_date__gte=date))
            
        # Sort by start date
        events = events.order_by('-start_date')
        
        response_serializer = EventListSerializer(events, many=True, context={'request': request})
        return Response(response_serializer.data)

    @action(detail=False, methods=['post'])
    def nearby(self, request):
        """Find events near the given coordinates"""
        serializer = NearbyEventsRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
        latitude = serializer.validated_data['latitude']
        longitude = serializer.validated_data['longitude']
        radius = serializer.validated_data.get('radius', 10)  # km
        
        # Use simplified nearby search
        nearby_events = Event.get_events_nearby(latitude, longitude, radius)
        
        response_serializer = self.get_serializer(nearby_events, many=True)
        return Response(response_serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated, CanJoinLeaveEvent])
    def join_event(self, request, pk=None):
        """
        Join an event with background face matching processing
        """
        event = self.get_object()
        user = request.user
        
        # Check if user is already attending
        attendance, created = EventAttendance.objects.get_or_create(
            user=user,
            event=event,
            defaults={'is_attending': True, 'attendance_method': 'SELF_JOIN'}
        )
        
        if not created and attendance.is_attending:
            return Response({'message': 'Already joined this event'}, status=status.HTTP_200_OK)
        
        # Mark as attending
        attendance.is_attending = True
        attendance.attendance_method = 'SELF_JOIN'
        attendance.save()
        
        logger.info(f"User {user.id} joined event {event.id}")
        
        # NEW: Enhanced face matching with enterprise queue
        face_matching_results = {
            'initiated': False,
            'processing_status': 'not_started'
        }
        
        # Check if user has verified facial profile
        if (hasattr(user, 'facial_profile') and 
            user.facial_profile.is_verified and 
            user.facial_profile.face_id):
            
            try:
                # Get photos that have face data but haven't been matched for this user
                photos_with_faces = EventPhoto.objects.filter(
                    event=event,
                    detected_faces__isnull=False
                ).exclude(
                    facematchresult__user=user
                )
                
                photo_count = photos_with_faces.count()
                
                if photo_count > 0:
                    try:
                        # NEW: Add to enterprise queue for face matching
                        photo_ids = [str(photo.id) for photo in photos_with_faces]
                        metadata = {
                            'event_id': str(event.id),
                            'user_id': str(user.id),  # Convert UUID to string
                            'batch_type': 'join_processing',
                            'processing_type': 'face_matching'
                        }
                        
                        success, queue_status = add_photos_to_queue(photo_ids, "HIGH", metadata)
                        
                        if success:
                            face_matching_results = {
                                'initiated': True,
                                'processing_status': 'queued',
                                'estimated_processing_time': self._estimate_processing_time(photo_count),
                                'photos_to_process': photo_count,
                                'queue_info': queue_status
                            }
                            logger.info(f"Successfully queued {photo_count} photos for face matching - user {user.id} joining event {event.id}")
                        else:
                            face_matching_results['processing_status'] = 'queue_error'
                            logger.error(f"Failed to queue photos for face matching - user {user.id} joining event {event.id}")
                            
                    except Exception as queue_error:
                        face_matching_results['processing_status'] = 'queue_error'
                        logger.error(f"Failed to queue photos for face matching - user {user.id} joining event {event.id}: {str(queue_error)}")
                else:
                    face_matching_results = {
                        'initiated': True,
                        'processing_status': 'completed',
                        'estimated_processing_time': '0 seconds',
                        'photos_to_process': 0
                    }
                    logger.info(f"No photos with face data found in event {event.id} - nothing to process for user {user.id}")
                    
            except Exception as e:
                face_matching_results['processing_status'] = 'error'
                logger.error(f"Error initiating face matching for user {user.id} joining event {event.id}: {str(e)}")
        
        else:
            logger.info(f"User {user.id} has no verified facial profile - skipping face matching")
            face_matching_results['processing_status'] = 'no_profile'
        
        # Return success with face matching queue results
        response_data = {
            'status': 'joined event',
            'face_matching_initiated': face_matching_results['initiated'],
            'face_processing': face_matching_results,
            'message': 'Successfully joined event. Face matching will be processed in the background.'
        }
        
        return Response(response_data, status=status.HTTP_200_OK)
        
    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated, CanJoinLeaveEvent])
    def leave_event(self, request, pk=None):
        """Leave an event"""
        event = self.get_object()
        user = request.user
        
        try:
            attendance = EventAttendance.objects.get(event=event, user=user)
            attendance.is_attending = False
            attendance.save()
            logger.info(f"User {user.id} left event {event.id}")
            return Response({'status': 'left event'})
        except EventAttendance.DoesNotExist:
            return Response({
                'error': 'You are not attending this event'
            }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def photos(self, request, pk=None):
        """Get event photos with role-based access control"""
        event = self.get_object()
        user = request.user
        
        # Get base queryset
        photos_queryset = EventPhoto.objects.filter(event=event)
        
        # Apply role-based filtering
        if event.creator == user:
            # EVENT CREATOR: See ALL photos
            filtered_photos = photos_queryset
            
        elif event.photographers.filter(id=user.id).exists():
            # PHOTOGRAPHER: See ONLY their own photos
            filtered_photos = photos_queryset.filter(photographer=user)
            
        elif EventAttendance.objects.filter(event=event, user=user, is_attending=True).exists():
            # ATTENDEE: See only photos they're tagged in
            filtered_photos = photos_queryset.filter(
                face_matches__user=user,
                face_matches__is_confirmed=True
            ).distinct()
            
        else:
            # NON-ATTENDEE: No access
            return Response(
                {'error': 'Permission denied'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Serialize and return
        serializer = EventPhotoSerializer(filtered_photos, many=True)
        return Response(serializer.data)

    # *** FIXED METHOD: Replace your add_photographer method in events/views.py ***
    @action(detail=True, methods=['post'])
    def add_photographer(self, request, pk=None):
        """Invite a photographer to an event (creates INVITED status)"""
        event = self.get_object()
        
        # Check if user is the creator
        if event.creator != request.user:
            return Response(
                {'error': 'Only event creator can invite photographers'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        photographer_email = request.data.get('photographer_email')
        photographer_id = request.data.get('photographer_id')
        
        if not photographer_email and not photographer_id:
            return Response(
                {'error': 'Either photographer_email or photographer_id is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Handle invitation by email
        if photographer_email:
            # Check if invitation already exists
            existing_invitation = EventUserRole.objects.filter(
                event=event,
                invitation_email=photographer_email,
                role=EventUserRole.RoleChoices.PHOTOGRAPHER
            ).first()
            
            if existing_invitation:
                return Response(
                    {'error': 'Photographer already invited or has a role in this event'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # *** FIXED: Check if photographer exists and set user field properly ***
            try:
                photographer_user = User.objects.get(email=photographer_email)
                
                # Check if user already has a role in this event
                existing_role = EventUserRole.objects.filter(
                    event=event,
                    user=photographer_user,
                    role=EventUserRole.RoleChoices.PHOTOGRAPHER
                ).first()
                
                if existing_role:
                    return Response(
                        {'error': 'Photographer already has a role in this event'}, 
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                # Create invitation WITH user field set
                photographer_role = EventUserRole.objects.create(
                    event=event,
                    user=photographer_user,  # *** FIXED: Set user field ***
                    invitation_email=photographer_email,
                    role=EventUserRole.RoleChoices.PHOTOGRAPHER,
                    invitation_status=EventUserRole.InvitationStatus.INVITED,
                    invited_by=request.user,
                    can_upload_photos=True,
                    can_set_photo_prices=True,
                    can_view_earnings=True,
                )
                
                # Send notification
                Notification.objects.create(
                    user=photographer_user,
                    notification_type='event_invitation',
                    title='Event Invitation',
                    message=f'You have been invited to photograph "{event.name}"',
                    event_name=event.name,
                    event=event
                )
                logger.info(f"Notification sent to photographer {photographer_user.id}")
                
            except User.DoesNotExist:
                # *** FIXED: For non-existing users, create email-only invitation ***
                photographer_role = EventUserRole.objects.create(
                    event=event,
                    # user=None (will be null, which is allowed for email invitations)
                    invitation_email=photographer_email,
                    role=EventUserRole.RoleChoices.PHOTOGRAPHER,
                    invitation_status=EventUserRole.InvitationStatus.INVITED,
                    invited_by=request.user,
                    can_upload_photos=True,
                    can_set_photo_prices=True,
                    can_view_earnings=True,
                )
                # Send email invitation for non-users (implement later)
                logger.info(f"Email invitation sent to {photographer_email}")
        
        # Handle invitation by user ID
        elif photographer_id:
            try:
                photographer_user = User.objects.get(id=photographer_id)
                
                # Check if user already has a role in this event
                existing_role = EventUserRole.objects.filter(
                    event=event,
                    user=photographer_user,
                    role=EventUserRole.RoleChoices.PHOTOGRAPHER
                ).first()
                
                if existing_role:
                    return Response(
                        {'error': 'Photographer already has a role in this event'}, 
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                # Create invitation by user ID
                photographer_role = EventUserRole.objects.create(
                    event=event,
                    user=photographer_user,
                    role=EventUserRole.RoleChoices.PHOTOGRAPHER,
                    invitation_status=EventUserRole.InvitationStatus.INVITED,
                    invited_by=request.user,
                    invitation_email=photographer_user.email,
                    can_upload_photos=True,
                    can_set_photo_prices=True,
                    can_view_earnings=True,
                )
                
                # Send notification
                Notification.objects.create(
                    user=photographer_user,
                    notification_type='event_invitation',
                    title='Event Invitation',
                    message=f'You have been invited to photograph "{event.name}"',
                    event_name=event.name,
                    event=event
                )
                
            except User.DoesNotExist:
                return Response(
                    {'error': 'Photographer not found'}, 
                    status=status.HTTP_404_NOT_FOUND
                )
        
        logger.info(f"Photographer invited to event {event.id} by {request.user.id}")
        return Response({
            'status': 'photographer invited',
            'message': 'Photographer has been invited and will receive a notification'
        })

    @action(detail=True, methods=['delete'])
    def remove_photographer(self, request, pk=None):
        """Remove a photographer from an event"""
        event = self.get_object()
        
        # Check if user is the creator
        if event.creator != request.user:
            return Response(
                {'error': 'Only event creator can remove photographers'}, 
                status=status.HTTP_403_FORBIDDEN
            )
            
        photographer_id = request.data.get('photographer_id')
        if photographer_id:
            event.photographers.remove(photographer_id)
            logger.info(f"Photographer {photographer_id} removed from event {event.id}")
            return Response({'status': 'photographer removed'})
        return Response({'error': 'photographer_id required'}, status=status.HTTP_400_BAD_REQUEST)
        
    @action(detail=True, methods=['post'])
    def update_price(self, request, pk=None):
        """Update image price limit for the event"""
        event = self.get_object()
        
        # Check if user is the creator
        if event.creator != request.user:
            return Response(
                {'error': 'Only event creator can update price limit'}, 
                status=status.HTTP_403_FORBIDDEN
            )
            
        price_limit = request.data.get('image_price_limit')
        if price_limit is not None:
            try:
                event.image_price_limit = float(price_limit)
                event.save(update_fields=['image_price_limit'])
                logger.info(f"Image price limit updated for event {event.id} to {price_limit}")
                return Response({'status': 'price limit updated'})
            except (ValueError, TypeError):
                return Response(
                    {'error': 'Invalid price value'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        return Response(
            {'error': 'image_price_limit is required'}, 
            status=status.HTTP_400_BAD_REQUEST
        )

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated, IsEventCreatorOrPhotographer])
    def upload_photo(self, request, pk=None):
        """
        Upload a single photo to an event with background face processing
        """
        event = self.get_object()
        
        try:
            serializer = EventPhotoUploadSerializer(data=request.data)
            if serializer.is_valid():
                image = serializer.validated_data['image']
                price = serializer.validated_data.get('price')

                # Validate price limit if set
                if price and event.image_price_limit and price > event.image_price_limit:
                    return Response(
                        {'error': f'Photo price ({price}) exceeds event limit ({event.image_price_limit})'},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Compress image before saving
                compressed_img = compress_image(image)

                # Create the photo
                photo = EventPhoto.objects.create(
                    event=event,
                    photographer=request.user,
                    price=price
                )

                # Save the compressed image
                photo.image.save(
                    f"{photo.id}_{image.name}",
                    ContentFile(compressed_img.read()),
                    save=True
                )

                logger.info(f"Photo {photo.id} uploaded to event {event.id} by {request.user.id}")

                # Update the event's photo count
                event.update_photo_counts()

                # NEW: Add photo to enterprise queue system
                photo_ids = [str(photo.id)]
                metadata = {
                    'event_id': str(event.id),
                    'user_id': str(request.user.id),  # Convert UUID to string
                    'batch_type': 'single_upload'
                }

                success, queue_status = add_photos_to_queue(photo_ids, "HIGH", metadata)

                # Return response with queue status
                return Response({
                    'message': 'Photo uploaded successfully',
                    'photo': EventPhotoSerializer(photo, context={'request': request}).data,
                    'face_processing': {
                        'status': 'queued' if success else 'error',
                        'queue_info': queue_status
                    }
                }, status=status.HTTP_201_CREATED)
            else:
                # Serializer is not valid - return validation errors
                return Response(
                    {'error': 'Invalid data', 'details': serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except Exception as e:
            logger.error(f"Error uploading photo to event {event.id}: {str(e)}")
            return Response(
                {'error': f'Error uploading photo: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated, IsEventCreatorOrPhotographer])
    def batch_upload_photos(self, request, pk=None):
        """
        Upload multiple photos to an event with background face processing
        Uses the enterprise queue system for optimal performance
        """
        event = self.get_object()
        
        try:
            serializer = BatchEventPhotoUploadSerializer(data=request.data)
            if serializer.is_valid():
                images = serializer.validated_data['images']
                prices = serializer.validated_data.get('prices', [])
                
                # Ensure prices list matches images length
                if prices and len(prices) != len(images):
                    return Response(
                        {'error': 'Number of prices must match number of images'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                # Pad prices with None if not provided
                if not prices:
                    prices = [None] * len(images)
                
                created_photos = []
                skipped_photos = []
                
                # Process each image
                with transaction.atomic():
                    for i, image in enumerate(images):
                        price = prices[i]
                        
                        # Validate price limit if set
                        if price and event.image_price_limit and price > event.image_price_limit:
                            skipped_photos.append({
                                'index': i,
                                'reason': f'Price {price} exceeds limit {event.image_price_limit}'
                            })
                            continue
                        
                        # Compress image before saving
                        compressed_img = compress_image(image)
                        
                        # Create the photo
                        photo = EventPhoto.objects.create(
                            event=event,
                            photographer=request.user,
                            price=price
                        )
                        
                        # Save the compressed image
                        photo.image.save(
                            f"{photo.id}_{image.name}",
                            ContentFile(compressed_img.read()),
                            save=True
                        )
                        
                        created_photos.append(photo)
                        logger.info(f"Photo {photo.id} batch uploaded to event {event.id} by {request.user.id}")
                    
                    # Update the event's photo count
                    event.update_photo_counts()
                
                # NEW: Add photos to enterprise queue system with smart prioritization
                if created_photos:
                    photo_ids = [str(photo.id) for photo in created_photos]
                    
                    # Smart priority assignment based on batch size
                    if len(photo_ids) <= 5:
                        priority = "HIGH"      # Small batches get high priority
                    elif len(photo_ids) <= 20:
                        priority = "STANDARD"  # Medium batches get standard priority
                    else:
                        priority = "LOW"       # Large batches get low priority
                    
                    metadata = {
                        'event_id': str(event.id),
                        'user_id': str(request.user.id),  # Convert UUID to string
                        'batch_type': 'batch_upload',
                        'batch_size': len(photo_ids)
                    }
                    
                    # Add to queue
                    success, queue_status = add_photos_to_queue(photo_ids, priority, metadata)
                    
                    if not success:
                        logger.error(f"Failed to add photos to processing queue for event {event.id}")
                
                # Prepare response
                response_data = {
                    'success': True,
                    'message': f'Successfully uploaded {len(created_photos)} photos',
                    'photos_uploaded': len(created_photos),
                    'photos_skipped': len(skipped_photos),
                    'photos': EventPhotoSerializer(created_photos, many=True, context={'request': request}).data,
                    'face_processing': {
                        'status': 'queued' if success else 'error',
                        'priority': priority if created_photos else 'none',
                        'estimated_processing_time': f'{len(created_photos) * 2} seconds',
                        'queue_info': queue_status if success else {}
                    }
                }
                
                # Add skipped photos info if any
                if skipped_photos:
                    response_data['skipped_photos'] = skipped_photos
                
                return Response(response_data, status=status.HTTP_201_CREATED)
                
        except Exception as e:
            logger.error(f"Error batch uploading photos to event {event.id}: {str(e)}")
            return Response(
                {'error': f'Error uploading photos: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def upload_status(self, request, pk=None):
        """Get upload status and timing information for an event"""
        try:
            event = self.get_object()
            upload_info = get_upload_status_info(event)
            
            return Response({
                'event_id': str(event.id),
                'event_name': event.name,
                'upload_info': upload_info
            })
            
        except Exception as e:
            logger.error(f"Error getting upload status for event {pk}: {str(e)}")
            return Response(
                {'error': f'Error getting upload status: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            ) 

    def _get_filtered_events_for_list(self):
        """
        Implement filtering logic for list action:
        - Show Available events: User hasn't joined, event within next 1 month or ongoing
        - Show Completed events: User joined and event completed
        - Show Ongoing events: User joined and event is ongoing
        - Show Scheduled events: User joined and event is in future
        - Hide events where deadline passed and user hasn't joined
        """
        if not self.request.user.is_authenticated:
            return Event.objects.none()
        
        user = self.request.user
        now = timezone.now()
        one_month_from_now = now + timedelta(days=30)
        
        # Get events where user is creator
        created_events = Event.objects.filter(creator=user)
        
        # Get events where user has joined as attendee
        attended_event_ids = EventAttendance.objects.filter(
            user=user, 
            is_attending=True
        ).values_list('event_id', flat=True)
        
        joined_events = Event.objects.filter(id__in=attended_event_ids)
        
        # Events where user has joined (creator or attendee) - show all regardless of timing
        user_joined_events = created_events.union(joined_events)
        
        # Events where user hasn't joined - apply availability logic
        not_joined_events = Event.objects.exclude(
            Q(creator=user) | Q(id__in=attended_event_ids)
        )
        
        # Filter not_joined_events to only show:
        # 1. Events within next month OR currently ongoing
        # 2. Events that haven't ended yet (deadline not passed)
        available_events = not_joined_events.filter(
            Q(start_date__lte=one_month_from_now) &  # Within next month
            (Q(end_date__isnull=True) | Q(end_date__gte=now))  # Not ended yet
        )
        
        # Combine all events that should be shown
        final_queryset = user_joined_events.union(available_events).order_by('-start_date')
        
        return final_queryset

    def get_serializer_class(self):
        """Return different serializers based on action"""
        if self.action == 'list':
            return EventListSerializer
        elif self.action == 'create':
            return EventCreateSerializer
        elif self.action == 'upload_photos':
            return EventPhotoUploadSerializer
        elif self.action == 'batch_upload_photos':
            return BatchEventPhotoUploadSerializer
        elif self.action == 'photos':
            return EventPhotoSerializer
        return EventSerializer

    def list(self, request, *args, **kwargs):
        """Enhanced list method with proper serializer context"""
        queryset = self.filter_queryset(self.get_queryset())
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)

    # Keep all other existing methods unchanged
    def perform_create(self, serializer):

        """Create new event and set creator"""
        event = serializer.save(creator=self.request.user)
        
        # Set jersey number requirement based on event type
        if event.event_type == Event.EventType.SPORTS:
            event.requires_jersey_number = True
            event.save(update_fields=['requires_jersey_number'])
            
        logger.info(f"Event created: {event.id} by user {self.request.user.id}")

    @action(detail=False, methods=['get'], url_path='photographer-events')
    def photographer_events(self, request):
        """
        Get events where the current user is assigned as photographer
        Includes enhanced filtering logic and status tags for photographers
        """
        if not request.user.is_authenticated:
            return Response({"detail": "Authentication required."}, status=status.HTTP_401_UNAUTHORIZED)
        
        # Get filtered events for photographer
        queryset = self._get_filtered_events_for_photographer()
        
        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = PhotographerEventListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = PhotographerEventListSerializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)
    
    def _get_filtered_events_for_photographer(self):
        """
        Implement filtering logic for photographer events:
        - Show Available events: User not assigned, event within next 1 month or ongoing
        - Show Completed events: User assigned and event completed
        - Show Ongoing events: User assigned and event is ongoing
        - Show Scheduled events: User assigned and event is in future
        - Hide events where deadline passed and user not assigned
        """
        if not self.request.user.is_authenticated:
            return Event.objects.none()
        
        user = self.request.user
        now = timezone.now()
        one_month_from_now = now + timedelta(days=30)
        
        # Get events where user is creator
        created_events = Event.objects.filter(creator=user)
        
        # Get events where user is assigned as photographer
        photographer_events = Event.objects.filter(photographers=user)
        
        # Events where user is assigned (creator or photographer) - show all regardless of timing
        user_assigned_events = created_events.union(photographer_events)
        
        # Events where user is not assigned - apply availability logic
        not_assigned_events = Event.objects.exclude(
            Q(creator=user) | Q(photographers=user)
        )
        
        # Filter not_assigned_events to only show:
        # 1. Events within next month OR currently ongoing
        # 2. Events that haven't ended yet (deadline not passed)
        available_events = not_assigned_events.filter(
            Q(start_date__lte=one_month_from_now) &  # Within next month
            (Q(end_date__isnull=True) | Q(end_date__gte=now))  # Not ended yet
        )
        
        # Combine all events that should be shown
        final_queryset = user_assigned_events.union(available_events).order_by('-start_date')
        
        return final_queryset

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated], url_path='queue-status')
    def global_queue_status(self, request):
        """
        Get global face processing queue status
        Now uses the enterprise queue system
        """
        try:
            queue_status = get_queue_status()
            
            return Response({
                'queue_status': queue_status,
                'timestamp': timezone.now().isoformat()
            })
        except Exception as e:
            logger.error(f"Error getting global queue status: {str(e)}")
            return Response({
                'queue_status': {
                    'status': 'error',
                    'error': str(e)
                },
                'timestamp': timezone.now().isoformat()
            })

    def _estimate_processing_time(self, photo_count: int) -> str:
        """Estimate processing time for photos"""
        total_seconds = photo_count * 2  # Estimate 2 seconds per photo
        
        if total_seconds < 60:
            return f"{total_seconds} seconds"
        elif total_seconds < 3600:
            minutes = total_seconds // 60
            return f"{minutes} minute(s)"
        else:
            hours = total_seconds // 3600
            return f"{hours} hour(s)"

    @action(detail=True, methods=['get'], permission_classes=[IsAuthenticated])
    def face_processing_status(self, request, pk=None):
        """
        Get comprehensive face processing status for an event
        Now uses the enterprise queue system
        """
        event = self.get_object()
        
        try:
            # NEW: Get queue status from enterprise system
            queue_status = get_queue_status()
            
            # Get event processing summary
            total_photos = EventPhoto.objects.filter(event=event).count()
            processed_photos = EventPhoto.objects.filter(event=event, processed_for_faces=True).count()
            
            # Get recent photos
            recent_photos = EventPhoto.objects.filter(event=event).order_by('-uploaded_at')[:10]
            
            response_data = {
                'event_summary': {
                    'total_photos': total_photos,
                    'processed_photos': processed_photos,
                    'processing_progress': (processed_photos / total_photos * 100) if total_photos > 0 else 100
                },
                'queue_status': queue_status,
                'recent_photos': EventPhotoSerializer(recent_photos, many=True, context={'request': request}).data,
                'last_updated': timezone.now().isoformat()
            }
            
            return Response(response_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error getting face processing status for event {event.id}: {str(e)}")
            return Response(
                {'error': f'Error getting processing status: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['get'])
    def join_processing_status(self, request, pk=None):
        try:
            event = self.get_object()
            user = request.user
            
            # Get user's face matches in this event
            from facial_recognition.models import FaceMatchResult
            user_matches = FaceMatchResult.objects.filter(
                event_photo__event=event,
                user=user
            )
            
            # Get event processing status
            total_photos = EventPhoto.objects.filter(event=event).count()
            processed_photos = EventPhoto.objects.filter(event=event, processed_for_faces=True).count()
            
            return Response({
                'join_processing': {
                    'status': 'completed' if processed_photos == total_photos else 'processing',
                    'photos_processed': processed_photos,
                    'matches_found': user_matches.count(),
                    'completion_percentage': (processed_photos / total_photos * 100) if total_photos > 0 else 0
                },
                'event_summary': {
                    'total_photos': total_photos,
                    'photos_processed': processed_photos,
                    'processing_complete_percentage': (processed_photos / total_photos * 100) if total_photos > 0 else 0,
                    'total_face_matches': FaceMatchResult.objects.filter(event_photo__event=event).count(),
                    'unique_users_matched': FaceMatchResult.objects.filter(event_photo__event=event).values('user').distinct().count()
                },
                'queue_status': face_queue.get_queue_status()
            })
            
        except Exception as e:
            logger.error(f"Error getting join processing status for event {pk}: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


    @action(detail=True, methods=['post'])
    def join_as_photographer(self, request, pk=None):
        """Join an event as photographer (auto-approve if invited, otherwise create request)"""
        event = self.get_object()
        user = request.user
        
        # Check if user is event creator (they can't join as photographer)
        if event.creator == user:
            return Response(
                {'error': 'Event creators cannot join as photographers'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # *** FIXED: Check for ANY existing photographer role first ***
        existing_role = EventUserRole.objects.filter(
            event=event,
            user=user,
            role=EventUserRole.RoleChoices.PHOTOGRAPHER
        ).first()
        
        # Check if user already has an active photographer role
        if existing_role:
            if existing_role.invitation_status == EventUserRole.InvitationStatus.APPROVED:
                return Response(
                    {'error': 'You are already a photographer for this event'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            elif existing_role.invitation_status == EventUserRole.InvitationStatus.PENDING_REQUEST:
                return Response(
                    {'error': 'You already have a pending request for this event'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            elif existing_role.invitation_status == EventUserRole.InvitationStatus.DENIED:
                if not existing_role.can_request_again():
                    return Response(
                        {'error': 'You have been denied 3 times and cannot request again'}, 
                        status=status.HTTP_403_FORBIDDEN
                    )
        
        # *** FIXED: Also check for email-only invitations ***
        email_invitation = EventUserRole.objects.filter(
            event=event,
            invitation_email=user.email,
            role=EventUserRole.RoleChoices.PHOTOGRAPHER,
            user__isnull=True,  # Email-only invitation
            invitation_status=EventUserRole.InvitationStatus.INVITED
        ).first()
        
        serializer = JoinAsPhotographerSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        # *** SCENARIO 1: User was invited (either by user_id or email) ***
        photographer_role = None
        
        # Check for user-based invitation
        if existing_role and existing_role.invitation_status == EventUserRole.InvitationStatus.INVITED:
            # Update existing invitation to approved
            existing_role.invitation_status = EventUserRole.InvitationStatus.APPROVED
            existing_role.approval_date = timezone.now()
            existing_role.request_message = serializer.validated_data.get('request_message', '')
            existing_role.portfolio_link = serializer.validated_data.get('portfolio_link', '')
            existing_role.specialization = serializer.validated_data.get('specialization', '')
            existing_role.save()
            photographer_role = existing_role
            
            auto_approved = True
            
        # Check for email-based invitation
        elif email_invitation:
            # Convert email invitation to user-based role
            email_invitation.user = user
            email_invitation.invitation_status = EventUserRole.InvitationStatus.APPROVED
            email_invitation.approval_date = timezone.now()
            email_invitation.request_message = serializer.validated_data.get('request_message', '')
            email_invitation.portfolio_link = serializer.validated_data.get('portfolio_link', '')
            email_invitation.specialization = serializer.validated_data.get('specialization', '')
            email_invitation.save()
            photographer_role = email_invitation
            
            auto_approved = True
            
        # *** SCENARIO 2: No invitation exists - create new request ***
        else:
            if existing_role and existing_role.invitation_status == EventUserRole.InvitationStatus.DENIED:
                # Update existing denied role to pending request
                existing_role.invitation_status = EventUserRole.InvitationStatus.PENDING_REQUEST
                existing_role.request_message = serializer.validated_data.get('request_message', '')
                existing_role.portfolio_link = serializer.validated_data.get('portfolio_link', '')
                existing_role.specialization = serializer.validated_data.get('specialization', '')
                existing_role.save()
                photographer_role = existing_role
            else:
                # Create new request
                photographer_role = EventUserRole.objects.create(
                    event=event,
                    user=user,
                    role=EventUserRole.RoleChoices.PHOTOGRAPHER,
                    invitation_status=EventUserRole.InvitationStatus.PENDING_REQUEST,
                    invitation_email=user.email,
                    request_message=serializer.validated_data.get('request_message', ''),
                    portfolio_link=serializer.validated_data.get('portfolio_link', ''),
                    specialization=serializer.validated_data.get('specialization', ''),
                    can_upload_photos=True,  # Will be blocked until approved
                    can_set_photo_prices=True,
                    can_view_earnings=True,
                )
            
            auto_approved = False
        
        # *** Send appropriate notifications ***
        if auto_approved:
            # Send notification to event creator
            Notification.objects.create(
                user=event.creator,
                notification_type='photographer_joined',
                title='Photographer Joined',
                message=f'{user.get_full_name() or user.email} has joined "{event.name}" as photographer',
                event_name=event.name,
                event=event
            )
            
            logger.info(f"Photographer {user.id} auto-approved for event {event.id}")
            return Response({
                'status': 'approved',
                'message': 'You have been automatically approved as photographer for this event'
            })
        else:
            # Send notification to event creator
            Notification.objects.create(
                user=event.creator,
                notification_type='photographer_request',
                title='Photographer Request',
                message=f'{user.get_full_name() or user.email} wants to photograph "{event.name}"',
                event_name=event.name,
                event=event
            )
            
            logger.info(f"Photographer {user.id} requested to join event {event.id}")
            return Response({
                'status': 'request_sent',
                'message': 'Your request has been sent to the event organizer for approval'
            })
        

    # *** NEW ENDPOINT ADDED ***
    @action(detail=True, methods=['get'])
    def photographer_requests(self, request, pk=None):
        """Get pending photographer requests for an event"""
        event = self.get_object()
        
        # Check if user is the creator
        if event.creator != request.user:
            return Response(
                {'error': 'Only event creator can view photographer requests'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        pending_requests = EventUserRole.objects.filter(
            event=event,
            role=EventUserRole.RoleChoices.PHOTOGRAPHER,
            invitation_status=EventUserRole.InvitationStatus.PENDING_REQUEST
        ).order_by('-created_at')
        
        serializer = PhotographerRequestSerializer(pending_requests, many=True)
        return Response(serializer.data)
    
    # *** NEW ENDPOINT ADDED ***
    @action(detail=True, methods=['post'])
    def approve_photographer_request(self, request, pk=None):
        """Approve a photographer request"""
        event = self.get_object()
        
        # Check if user is the creator
        if event.creator != request.user:
            return Response(
                {'error': 'Only event creator can approve photographer requests'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        request_id = request.data.get('request_id')
        if not request_id:
            return Response(
                {'error': 'request_id is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            photographer_request = EventUserRole.objects.get(
                id=request_id,
                event=event,
                role=EventUserRole.RoleChoices.PHOTOGRAPHER,
                invitation_status=EventUserRole.InvitationStatus.PENDING_REQUEST
            )
            
            # Approve the request
            photographer_request.invitation_status = EventUserRole.InvitationStatus.APPROVED
            photographer_request.approval_date = timezone.now()
            photographer_request.reset_denial_count()  # Reset denial count
            photographer_request.save()
            
            # Send notification to photographer
            Notification.objects.create(
                user=photographer_request.user,
                notification_type='request_approved',
                title='Request Approved',
                message=f'Your request to photograph "{event.name}" has been approved!',
                event_name=event.name,
                event=event
            )
            
            logger.info(f"Photographer request {request_id} approved by {request.user.id}")
            return Response({
                'status': 'approved',
                'message': 'Photographer request has been approved'
            })
            
        except EventUserRole.DoesNotExist:
            return Response(
                {'error': 'Photographer request not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
    @action(detail=True, methods=['post'])
    def deny_photographer_request(self, request, pk=None):
        """Deny a photographer request"""
        event = self.get_object()
        
        # Check if user is the creator
        if event.creator != request.user:
            return Response(
                {'error': 'Only event creator can deny photographer requests'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        request_id = request.data.get('request_id')
        denial_reason = request.data.get('reason', 'No reason provided')
        
        if not request_id:
            return Response(
                {'error': 'request_id is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            photographer_request = EventUserRole.objects.get(
                id=request_id,
                event=event,
                role=EventUserRole.RoleChoices.PHOTOGRAPHER,
                invitation_status=EventUserRole.InvitationStatus.PENDING_REQUEST
            )
            
            # Deny the request and increment denial count
            photographer_request.invitation_status = EventUserRole.InvitationStatus.DENIED
            photographer_request.increment_denial_count()
            
            # Send notification to photographer
            Notification.objects.create(
                user=photographer_request.user,
                notification_type='request_denied',
                title='Request Denied',
                message=f'Your request to photograph "{event.name}" has been denied. Reason: {denial_reason}',
                event_name=event.name,
                event=event
            )
            
            logger.info(f"Photographer request {request_id} denied by {request.user.id}")
            return Response({
                'status': 'denied',
                'message': 'Photographer request has been denied'
            })
            
        except EventUserRole.DoesNotExist:
            return Response(
                {'error': 'Photographer request not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )