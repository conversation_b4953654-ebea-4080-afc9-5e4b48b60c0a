from django.contrib import admin
from .models import Event, EventUserRole, EventPhoto, EventAttendance
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone



class EventPhotoInline(admin.TabularInline):
    model = EventPhoto
    extra = 0
    readonly_fields = ['id', 'uploaded_at', 'processed_for_faces']
    fields = ['id', 'image', 'photographer', 'price', 'uploaded_at', 'processed_for_faces']


class EventAttendanceInline(admin.TabularInline):
    model = EventAttendance
    extra = 0
    readonly_fields = ['id', 'joined_at']
    fields = ['id', 'user', 'is_attending', 'attendance_method', 'joined_at', 'jersey_number']


@admin.register(Event)
class EventAdmin(admin.ModelAdmin):
    list_display = ['id', 'name', 'creator', 'event_type', 'start_date', 'end_date', 'visibility', 'total_photos']
    list_filter = ['event_type', 'visibility', 'start_date']
    search_fields = ['name', 'description', 'location']
    readonly_fields = ['id', 'created_at', 'updated_at', 'total_photos', 'tagged_photos']
    filter_horizontal = ['photographers']
    inlines = [EventAttendanceInline, EventPhotoInline]
    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'name', 'creator', 'description', 'event_type', 'visibility')
        }),
        ('Dates and Location', {
            'fields': ('start_date', 'end_date', 'location', 'latitude', 'longitude')
        }),
        ('Settings', {
            'fields': ('subscription', 'photographers', 'image_price_limit', 'allow_user_uploads', 'requires_jersey_number')
        }),
        ('Media', {
            'fields': ('banner_image', 'qr_code')
        }),
        ('Statistics', {
            'fields': ('total_photos', 'tagged_photos', 'created_at', 'updated_at')
        }),
    )
    
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.prefetch_related('photographers', 'attendances')


@admin.register(EventAttendance)
class EventAttendanceAdmin(admin.ModelAdmin):
    list_display = ['id', 'event', 'user', 'is_attending', 'attendance_method', 'joined_at']
    list_filter = ['is_attending', 'attendance_method', 'joined_at']
    search_fields = ['event__name', 'user__email', 'user__username']
    readonly_fields = ['id', 'joined_at']


@admin.register(EventPhoto)
class EventPhotoAdmin(admin.ModelAdmin):
    list_display = ['id', 'event', 'photographer', 'uploaded_at', 'processed_for_faces']
    list_filter = ['uploaded_at', 'processed_for_faces']
    search_fields = ['event__name', 'photographer__email', 'photographer__username']
    readonly_fields = ['id', 'uploaded_at', 'detected_faces']
    fieldsets = (
        ('Photo Information', {
            'fields': ('id', 'event', 'photographer', 'image', 'price')
        }),
        ('Processing Status', {
            'fields': ('processed_for_faces', 'detected_faces', 'uploaded_at')
        }),
    )


@admin.register(EventUserRole)
class EventUserRoleAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'user_email', 'event_name', 'role', 'invitation_status', 
        'invited_by_email', 'created_at', 'approval_date', 'denial_count'
    ]
    list_filter = [
        'role', 'invitation_status', 'created_at', 'approval_date',
        'can_upload_photos', 'can_set_photo_prices', 'is_active'
    ]
    search_fields = [
        'user__email', 'user__username', 'event__name', 
        'invitation_email', 'invited_by__email'
    ]
    readonly_fields = [
        'id', 'created_at', 'updated_at', 'joined_at'
    ]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'user', 'event', 'role', 'invitation_email')
        }),
        ('Invitation Details', {
            'fields': (
                'invitation_status', 'invited_by', 'request_message', 
                'portfolio_link', 'specialization', 'approval_date', 'denial_count'
            )
        }),
        ('Permissions', {
            'fields': (
                'can_upload_photos', 'can_set_photo_prices', 'can_delete_own_photos',
                'can_edit_own_photos', 'can_view_earnings', 'can_manage_event',
                'can_view_analytics', 'can_assign_roles', 'can_download_hd_free',
                'can_view_all_photos', 'can_remove_content', 'can_invite_photographers',
                'can_set_filters'
            ),
            'classes': ['collapse']
        }),
        ('Event Participation', {
            'fields': (
                'assigned_via', 'is_active', 'jersey_number', 
                'can_view_tagged_photos', 'can_purchase_photos'
            )
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'joined_at'),
            'classes': ['collapse']
        })
    )
    
    # Custom display methods
    def user_email(self, obj):
        if obj.user:
            return obj.user.email
        return obj.invitation_email or "No email"
    user_email.short_description = "User Email"
    user_email.admin_order_field = 'user__email'
    
    def event_name(self, obj):
        return obj.event.name
    event_name.short_description = "Event"
    event_name.admin_order_field = 'event__name'
    
    def invited_by_email(self, obj):
        return obj.invited_by.email if obj.invited_by else "N/A"
    invited_by_email.short_description = "Invited By"
    invited_by_email.admin_order_field = 'invited_by__email'
    
    # Custom actions
    actions = ['approve_photographer_requests', 'deny_photographer_requests', 'send_invitation_reminder']
    
    def approve_photographer_requests(self, request, queryset):
        """Bulk approve photographer requests"""
        updated = queryset.filter(
            invitation_status=EventUserRole.InvitationStatus.PENDING_REQUEST
        ).update(
            invitation_status=EventUserRole.InvitationStatus.APPROVED,
            approval_date=timezone.now()
        )
        self.message_user(request, f'{updated} photographer requests approved.')
    approve_photographer_requests.short_description = "Approve selected photographer requests"
    
    def deny_photographer_requests(self, request, queryset):
        """Bulk deny photographer requests"""
        updated = queryset.filter(
            invitation_status=EventUserRole.InvitationStatus.PENDING_REQUEST
        ).update(
            invitation_status=EventUserRole.InvitationStatus.DENIED
        )
        self.message_user(request, f'{updated} photographer requests denied.')
    deny_photographer_requests.short_description = "Deny selected photographer requests"
    
    def send_invitation_reminder(self, request, queryset):
        """Send reminder for pending invitations"""
        # Implement email reminder logic here
        count = queryset.filter(
            invitation_status=EventUserRole.InvitationStatus.INVITED
        ).count()
        self.message_user(request, f'Reminder sent for {count} pending invitations.')
    send_invitation_reminder.short_description = "Send invitation reminders"
    
    # Enable adding new photographer requests from admin
    def has_add_permission(self, request):
        return True
    
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.select_related('user', 'event', 'invited_by')