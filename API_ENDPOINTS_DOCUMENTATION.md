# PhotoFish Backend API Endpoints Documentation

## 🔐 Authentication
All endpoints require JWT authentication:
```
Authorization: Bearer {your_jwt_token}
```

---

## 🎯 Event Organizer Endpoints

### Event Management
| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/events/` | List all events |
| `POST` | `/api/v1/events/` | Create new event |
| `GET` | `/api/v1/events/{event_id}/` | Get event details |
| `PUT` | `/api/v1/events/{event_id}/` | Update event |
| `DELETE` | `/api/v1/events/{event_id}/` | Delete event |
| `GET` | `/api/v1/events/{event_id}/details/` | Get detailed event info |
| `POST` | `/api/v1/events/{event_id}/toggle-visibility/` | Toggle event visibility |

### Event Lists & Search
| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/events/created/` | Events you created |
| `GET` | `/api/v1/events/home/<USER>/` | Home screen events |
| `POST` | `/api/v1/events/home/<USER>/` | Nearby events |
| `POST` | `/api/v1/events/search/` | Search events |

### Photographer Management (Organizer Only)
| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/events/{event_id}/photographer-requests/` | View pending photographer requests |
| `POST` | `/api/v1/events/{event_id}/approve-photographer/` | Approve photographer request |
| `POST` | `/api/v1/events/{event_id}/deny-photographer/` | Deny photographer request |
| `POST` | `/api/v1/events/{event_id}/photographers/` | Add photographer directly |
| `DELETE` | `/api/v1/events/{event_id}/photographers/` | Remove photographer |

### Event Photos & Content
| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/events/{event_id}/photos/` | Get event photos |
| `POST` | `/api/v1/events/{event_id}/upload-photo/` | Upload single photo |
| `POST` | `/api/v1/events/{event_id}/batch-upload-photos/` | Upload multiple photos |
| `POST` | `/api/v1/events/{event_id}/update-price/` | Update photo pricing |

### Event Attendance
| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/events/attended/` | Events you attended |
| `POST` | `/api/v1/events/{event_id}/join/` | Join event as attendee |
| `POST` | `/api/v1/events/{event_id}/leave/` | Leave event |

---

## 📸 Photographer Endpoints

### Photographer Dashboard
| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/events/photographer/dashboard/` | Complete dashboard data |
| `GET` | `/api/v1/events/photographer/dashboard/stats/` | Dashboard statistics |
| `GET` | `/api/v1/events/photographer/notifications/` | Photographer notifications |
| `GET` | `/api/v1/events/photographer/recent-events/` | Recent events |
| `GET` | `/api/v1/events/photographer/nearby-events/` | Available nearby events |
| `GET` | `/api/v1/events/photographer/quick-upload/check/` | Check for active events (Quick Add Photos) |

### Photographer Events & Status
| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/events/photographer-events/` | Events where you're photographer (with status) |
| `POST` | `/api/v1/events/{event_id}/join-as-photographer/` | Request to join as photographer |
| `GET` | `/api/v1/events/debug-photographer-roles/` | Debug: Check your photographer roles |

### Photographer Photos
| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/photographers/my-photos/` | All your uploaded photos |
| `GET` | `/api/v1/photographers/{event_id}/my-photos/` | Your photos for specific event |
| `POST` | `/api/v1/events/{event_id}/upload-photo/` | Upload photo to event |
| `POST` | `/api/v1/events/{event_id}/batch-upload-photos/` | Batch upload photos |

---

## 🔄 Queue & Processing Status
| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/api/v1/events/queue-status/` | Global queue status |
| `GET` | `/api/v1/events/{event_id}/face-processing-status/` | Event face processing status |
| `GET` | `/api/v1/events/{event_id}/join-processing-status/` | Join processing status |

---

## 📋 Request/Response Examples

### Join as Photographer
```bash
POST /api/v1/events/{event_id}/join-as-photographer/
Content-Type: application/json

{
    "request_message": "I would love to photograph your event!",
    "portfolio_link": "https://myportfolio.com",
    "specialization": "Wedding Photography"
}
```

**Response (Auto-approved if invited):**
```json
{
    "status": "approved",
    "message": "Successfully joined as photographer"
}
```

**Response (Request sent):**
```json
{
    "status": "request_sent",
    "message": "Your photographer request has been sent to the event organizer"
}
```

### Approve Photographer Request
```bash
POST /api/v1/events/{event_id}/approve-photographer/
Content-Type: application/json

{
    "request_id": "photographer_role_id"
}
```

### Deny Photographer Request
```bash
POST /api/v1/events/{event_id}/deny-photographer/
Content-Type: application/json

{
    "request_id": "photographer_role_id",
    "denial_reason": "Not suitable for this event"
}
```

### Get Photographer Events (with status)
```bash
GET /api/v1/events/photographer-events/
```

**Response:**
```json
{
    "count": 3,
    "results": [
        {
            "id": "event-uuid",
            "name": "Wedding Event",
            "user_relation": "pending_request",
            "attendance_type": "Pending Photographer",
            "event_status": "available"
        }
    ]
}
```

### Get Pending Photographer Requests (Organizer Only)
```bash
GET /api/v1/events/{event_id}/photographer-requests/
```

**Response:**
```json
[
    {
        "id": 123,
        "photographer_name": "John Doe",
        "photographer_email": "<EMAIL>",
        "event_name": "Wedding Reception",
        "request_message": "I would love to photograph your event!",
        "requested_date": "2024-01-15T10:30:00Z",
        "invitation_status": "PENDING_REQUEST",
        "denial_count": 0,
        "specialization": "Wedding Photography",
        "portfolio_link": "https://myportfolio.com"
    }
]
```

### Photographer Dashboard
```bash
GET /api/v1/events/photographer/dashboard/
```

**Response:**
```json
{
    "success": true,
    "dashboard": {
        "stats": {
            "total_images": 150,
            "total_events": 12,
            "total_revenue": "2500.00",
            "pending_disbursement": "450.00"
        },
        "quick_upload": {
            "has_active_events": true,
            "events": [
                {
                    "id": "event-uuid",
                    "name": "Wedding Event",
                    "status": "in_progress"
                }
            ]
        },
        "recent_events": [...],
        "nearby_events": [...]
    }
}
```

---

## 📊 Status Values & Meanings

### User Relations (`user_relation` field)
| Status | Description |
|--------|-------------|
| `"creator"` | You created the event |
| `"photographer"` | Approved photographer |
| `"pending_request"` | Photographer request pending approval |
| `"invited"` | Invited by organizer (not yet joined) |
| `"denied"` | Photographer request was denied |
| `"joined"` | Joined as attendee |
| `"not_joined"` | No relation to event |

### Attendance Types (`attendance_type` field)
| Type | Description |
|------|-------------|
| `"Created"` | You created the event |
| `"Photographer"` | Approved photographer |
| `"Pending Photographer"` | Photographer request pending |
| `"Invited Photographer"` | Invited but not yet joined |
| `"Attended"` | Joined as attendee |
| `null` | No relation to event |

### Event Status (`event_status` field)
| Status | Description |
|--------|-------------|
| `"available"` | Can request to join as photographer |
| `"scheduled"` | Upcoming event |
| `"ongoing"` | Currently happening |
| `"completed"` | Finished event |

### Invitation Status (EventUserRole)
| Status | Description |
|--------|-------------|
| `"INVITED"` | Invited by organizer |
| `"PENDING_REQUEST"` | Request pending approval |
| `"APPROVED"` | Request approved |
| `"DENIED"` | Request denied |
| `"CANCELLED"` | Request cancelled |

---

## 🚀 Common Workflows

### For Event Organizers

1. **Create Event**
   ```
   POST /api/v1/events/
   ```

2. **View Photographer Requests**
   ```
   GET /api/v1/events/{event_id}/photographer-requests/
   ```

3. **Approve/Deny Requests**
   ```
   POST /api/v1/events/{event_id}/approve-photographer/
   POST /api/v1/events/{event_id}/deny-photographer/
   ```

### For Photographers

1. **Check Available Events**
   ```
   GET /api/v1/events/photographer-events/
   ```

2. **Request to Join Event**
   ```
   POST /api/v1/events/{event_id}/join-as-photographer/
   ```

3. **Check Request Status**
   ```
   GET /api/v1/events/photographer-events/
   GET /api/v1/events/debug-photographer-roles/
   ```

4. **Upload Photos (when approved)**
   ```
   POST /api/v1/events/{event_id}/upload-photo/
   ```

---

## 🔧 Error Handling

### Common Error Responses

**400 Bad Request:**
```json
{
    "error": "You already have a pending request for this event"
}
```

**403 Forbidden:**
```json
{
    "error": "Only event creator can view photographer requests"
}
```

**404 Not Found:**
```json
{
    "detail": "Not found."
}
```

---

## 📝 Notes

- All timestamps are in ISO 8601 format with timezone
- Event IDs and User IDs are UUIDs
- File uploads use multipart/form-data
- Pagination is available on list endpoints
- Rate limiting may apply to upload endpoints

---

*Last updated: July 2025*
```
