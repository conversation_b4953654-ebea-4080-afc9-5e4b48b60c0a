# *** REPLACE THE EMPTY MIGRATION FILE CONTENT WITH THIS ***
# events/migrations/000X_safe_uuid_migration.py

import uuid
from django.db import migrations, models

def add_uuid_to_existing_records(apps, schema_editor):
    """Safely add UUIDs to existing EventUserRole records"""
    EventUserRole = apps.get_model('events', 'EventUserRole')
    
    # Get all existing records
    existing_records = list(EventUserRole.objects.all())
    
    # Generate unique UUIDs for each record
    for record in existing_records:
        # Generate a new UUID and ensure it's unique
        new_uuid = uuid.uuid4()
        
        # Double-check uniqueness (very unlikely to be needed, but safety first)
        while EventUserRole.objects.filter(temp_uuid=new_uuid).exists():
            new_uuid = uuid.uuid4()
        
        # Set the UUID
        record.temp_uuid = new_uuid
        record.save(update_fields=['temp_uuid'])
    
    print(f"✅ Added UUIDs to {len(existing_records)} existing EventUserRole records")

def reverse_uuid_addition(apps, schema_editor):
    """Reverse the UUID addition"""
    EventUserRole = apps.get_model('events', 'EventUserRole')
    EventUserRole.objects.update(temp_uuid=None)

class Migration(migrations.Migration):
    dependencies = [
        ('events', '0006_alter_eventuserrole_user'),  # Adjust this to your latest migration
    ]

    operations = [
        # Step 1: Add a temporary UUID field
        migrations.AddField(
            model_name='eventuserrole',
            name='temp_uuid',
            field=models.UUIDField(null=True, blank=True),
        ),
        
        # Step 2: Populate UUIDs for existing records
        migrations.RunPython(
            add_uuid_to_existing_records,
            reverse_uuid_addition,
        ),
        
        # Step 3: Make temp_uuid non-nullable and unique
        migrations.AlterField(
            model_name='eventuserrole',
            name='temp_uuid',
            field=models.UUIDField(unique=True, default=uuid.uuid4),
        ),
        
        # Step 4: Remove the old integer id field
        migrations.RemoveField(
            model_name='eventuserrole',
            name='id',
        ),
        
        # Step 5: Rename temp_uuid to id
        migrations.RenameField(
            model_name='eventuserrole',
            old_name='temp_uuid',
            new_name='id',
        ),
        
        # Step 6: Make the new id field the primary key
        migrations.AlterField(
            model_name='eventuserrole',
            name='id',
            field=models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False),
        ),
    ]


# *** ALTERNATIVE SIMPLE APPROACH IF YOU CAN DELETE DATA ***
# If you don't have important EventUserRole data, use this simpler migration:

"""
class Migration(migrations.Migration):
    dependencies = [
        ('events', '0006_alter_eventuserrole_user'),
    ]

    operations = [
        # Delete all existing data to avoid conflicts
        migrations.RunSQL(
            "DELETE FROM event_user_role;",
            reverse_sql="-- No reverse operation"
        ),
        
        # Change the id field to UUID
        migrations.AlterField(
            model_name='eventuserrole',
            name='id',
            field=models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False),
        ),
    ]
"""