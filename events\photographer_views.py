# 📋 STEP 2: CREATE API VIEWS FOR PHOTOGRAPHER DASHBOARD

# Create a new file: events/photographer_views.py

from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.db.models import Count, Sum, Q
from django.utils.decorators import method_decorator
from django.views import View
from django.utils import timezone
from datetime import timedelta
from decimal import Decimal

from .models import Event, EventAttendance, EventPhoto, Notification, PhotoSale
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def photographer_dashboard_stats(request):
    """Get photographer dashboard statistics - the 3 top cards"""
    photographer = request.user
    
    # Get total images taken by this photographer
    total_images = EventPhoto.objects.filter(
        photographer=photographer
    ).count()
    
    # Get total events attended as photographer
    total_events = EventAttendance.objects.filter(
        user=photographer
    ).values('event').distinct().count()
    
    # Calculate total revenue and pending disbursement from PhotoSale model
    photo_sales = PhotoSale.objects.filter(photographer=photographer)
    total_revenue = photo_sales.aggregate(
        total=Sum('photographer_commission')
    )['total'] or Decimal('0.00')
    
    pending_disbursement = photo_sales.filter(
        is_paid_to_photographer=False
    ).aggregate(
        total=Sum('photographer_commission')
    )['total'] or Decimal('0.00')
    
    return Response({
        'success': True,
        'data': {
            'total_images': {
                'count': total_images,
                'label': 'TOTAL IMAGES TAKEN'
            },
            'total_events': {
                'count': total_events,
                'label': 'TOTAL EVENTS ATTENDED'
            },
            'total_revenue': {
                'amount': str(total_revenue),
                'pending_disbursement': str(pending_disbursement),
                'label': 'TOTAL REVENUE'
            }
        }
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def quick_upload_check(request):
    """Check for active events for Quick Add Photos button"""
    photographer = request.user
    now = timezone.now()
    
    # Find active events (events happening today or recently)
    active_events = Event.objects.filter(
        photographers=photographer,
        start_date__lte=now + timedelta(hours=6),  # Events starting within 6 hours
        start_date__gte=now - timedelta(hours=12)  # Events that started within 12 hours
    ).order_by('start_date')
    
    if active_events.exists():
        # If there are active events, return them
        events_data = []
        for event in active_events:
            events_data.append({
                'id': str(event.id),
                'name': event.name,
                'start_date': event.start_date.isoformat() if event.start_date else None,
                'status': 'in_progress' if event.start_date <= now.date() else 'upcoming',
                'photo_count': event.total_photos
            })
        
        return Response({
            'success': True,
            'has_active_events': True,
            'events': events_data,
            'default_action': 'show_event_selector' if len(events_data) > 1 else 'direct_upload',
            'primary_event': events_data[0] if events_data else None
        })
    else:
        return Response({
            'success': True,
            'has_active_events': False,
            'message': 'No active events found',
            'suggestion': 'Check "Events Near By" section for new opportunities'
        })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def photographer_notifications(request):
    """Get photographer notifications"""
    photographer = request.user
    
    # Get recent unread notifications (limit 5 for dashboard)
    notifications = Notification.objects.filter(
        user=photographer,
        is_read=False
    ).order_by('-created_at')[:5]
    
    notifications_data = []
    for notification in notifications:
        # *** UPDATED: Map notification type to icon (added new types) ***
        icon_map = {
            'payment_received': 'dollar-sign',
            'payment_pending': 'clock',
            'payment_disbursed': 'dollar-sign',
            'event_accepted': 'calendar-check',
            'event_invitation': 'calendar-plus',           # Existing
            'photo_processed': 'camera',
            # *** NEW ICON MAPPINGS ADDED ***
            'photographer_request': 'user-plus',          # When someone requests to join your event (organizers)
            'photographer_joined': 'user-check',          # When photographer joins your event (organizers)
            'request_approved': 'check-circle',           # When your request is approved (photographers)
            'request_denied': 'x-circle',                 # When your request is denied (photographers)
        }
        
        notifications_data.append({
            'id': str(notification.id),
            'type': notification.notification_type,
            'title': notification.title,
            'message': notification.message,
            'icon': icon_map.get(notification.notification_type, 'bell'),
            'amount': str(notification.amount) if notification.amount else None,
            'event_name': notification.event_name,
            'time_ago': get_time_ago(notification.created_at),
            'created_at': notification.created_at.isoformat()
        })
    
    return Response({
        'success': True,
        'notifications': notifications_data,
        'unread_count': notifications.count()
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def recent_events(request):
    """Get photographer's recent events"""
    photographer = request.user
    
    # Get recent events where user is photographer
    recent_events = Event.objects.filter(
        photographers=photographer
    ).order_by('-start_date')[:5]
    
    events_data = []
    for event in recent_events:
        # Determine event status
        now = timezone.now().date()
        if event.start_date and event.start_date > now:
            status = 'Upcoming'
            status_class = 'upcoming'
        elif event.start_date and event.start_date <= now and (event.end_date is None or event.end_date >= now):
            status = 'In Progress'
            status_class = 'in-progress'
        else:
            status = 'Completed'
            status_class = 'completed'
        
        events_data.append({
            'id': str(event.id),
            'name': event.name,
            'date': event.start_date.strftime('%b %d, %Y') if event.start_date else 'TBD',
            'status': status,
            'status_class': status_class,
            'photo_count': event.total_photos,
            'location': event.location
        })
    
    return Response({
        'success': True,
        'events': events_data
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def nearby_events(request):
    """Get events near photographer that need photographers"""
    photographer = request.user
    
    # Get upcoming events that need photographers (not assigned to this photographer)
    nearby_events = Event.objects.filter(
        start_date__gte=timezone.now().date(),
        visibility='PUBLIC'  # Use your Event.EventVisibility.PUBLIC
    ).exclude(
        photographers=photographer
    ).order_by('start_date')[:5]
    
    events_data = []
    for event in nearby_events:
        # Check if photographer has already applied (you might need to create an EventApplication model)
        has_applied = False  # Placeholder - implement based on your application system
        
        events_data.append({
            'id': str(event.id),
            'name': event.name,
            'paying_type': event.paying_type,
            'date_time': event.start_date.strftime('%b %d, %Y - %I:%M %p') if event.start_date else 'TBD',
            'location': event.location or 'Location TBD',
            'status': 'Applied' if has_applied else 'Available',
            'status_class': 'applied' if has_applied else 'available',
            'action': 'Applied' if has_applied else 'Apply'
        })
    
    return Response({
        'success': True,
        'events': events_data
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def complete_dashboard(request):
    """Single endpoint to get all dashboard data at once"""
    photographer = request.user
    
    # Get all data
    try:
        # Stats
        total_images = EventPhoto.objects.filter(photographer=photographer).count()
        total_events = EventAttendance.objects.filter(user=photographer).values('event').distinct().count()
        
        photo_sales = PhotoSale.objects.filter(photographer=photographer)
        total_revenue = photo_sales.aggregate(total=Sum('photographer_commission'))['total'] or Decimal('0.00')
        pending_disbursement = photo_sales.filter(is_paid_to_photographer=False).aggregate(
            total=Sum('photographer_commission'))['total'] or Decimal('0.00')
        
        stats = {
            'total_images': {'count': total_images, 'label': 'TOTAL IMAGES TAKEN'},
            'total_events': {'count': total_events, 'label': 'TOTAL EVENTS ATTENDED'},
            'total_revenue': {
                'amount': str(total_revenue),
                'pending_disbursement': str(pending_disbursement),
                'label': 'TOTAL REVENUE'
            }
        }
        
        # Quick upload check
        now = timezone.now()
        active_events = Event.objects.filter(
            photographers=photographer,
            start_date__lte=now + timedelta(hours=6),
            start_date__gte=now - timedelta(hours=12)
        ).order_by('start_date')
        
        quick_upload = {
            'has_active_events': active_events.exists(),
            'primary_event': {
                'id': str(active_events.first().id),
                'name': active_events.first().name
            } if active_events.exists() else None
        }
        
        # Notifications
        notifications = Notification.objects.filter(
            user=photographer, is_read=False
        ).order_by('-created_at')[:5]
        
        notifications_data = [{
            'id': str(n.id),
            'type': n.notification_type,
            'title': n.title,
            'message': n.message,
            'time_ago': get_time_ago(n.created_at)
        } for n in notifications]
        
        # Recent events
        recent_events_qs = Event.objects.filter(photographers=photographer).order_by('-start_date')[:5]
        recent_events_data = [{
            'id': str(e.id),
            'name': e.name,
            'date': e.start_date.strftime('%b %d, %Y') if e.start_date else 'TBD',
            'status': 'Completed'  # Simplified status
        } for e in recent_events_qs]
        
        # Nearby events
        nearby_events_qs = Event.objects.filter(
            start_date__gte=timezone.now().date(),
            visibility='PUBLIC'
        ).exclude(photographers=photographer).order_by('start_date')[:5]
        
        nearby_events_data = [{
            'id': str(e.id),
            'name': e.name,
            'paying_type': e.paying_type,
            'date_time': e.start_date.strftime('%b %d, %Y') if e.start_date else 'TBD',
            'location': e.location or 'Location TBD',
            'status': 'Available'
        } for e in nearby_events_qs]
        
        return Response({
            'success': True,
            'dashboard': {
                'stats': stats,
                'quick_upload': quick_upload,
                'notifications': notifications_data,
                'recent_events': recent_events_data,
                'nearby_events': nearby_events_data
            },
            'generated_at': timezone.now().isoformat()
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=500)


def get_time_ago(created_at):
    """Convert datetime to human readable time ago"""
    now = timezone.now()
    diff = now - created_at
    
    if diff.days > 0:
        return f"{diff.days} days ago"
    elif diff.seconds > 3600:
        hours = diff.seconds // 3600
        return f"{hours} hours ago"
    else:
        minutes = diff.seconds // 60
        return f"{minutes} minutes ago"
    

def create_photographer_notification(user, notification_type, title, message, event=None, event_name=None, amount=None):
    """Helper function to create photographer notifications"""
    try:
        notification = Notification.objects.create(
            user=user,
            notification_type=notification_type,
            title=title,
            message=message,
            event=event,
            event_name=event_name,
            amount=amount
        )
        logger.info(f"Notification created: {notification_type} for user {user.id}")
        return notification
    except Exception as e:
        logger.error(f"Failed to create notification: {e}")
        return None
