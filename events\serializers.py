# events/serializers.py - Complete clean version

from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from users.serializers import UserSerializer
from .models import Event, EventAttendance, EventPhoto, EventUserRole
from .utils import compress_image



# Get the user model dynamically to avoid import issues
User = get_user_model()


class EventPhotographerSerializer(serializers.ModelSerializer):
    """Simple serializer for photographers in events"""
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'email']


class EventPhotoSerializer(serializers.ModelSerializer):
    """Serializer for event photos"""
    photographer_id = serializers.IntegerField(source='photographer.id', read_only=True)
    photographer_username = serializers.CharField(source='photographer.username', read_only=True)
    photographer_email = serializers.EmailField(source='photographer.email', read_only=True)
    photographer_name = serializers.SerializerMethodField()
    
    is_user_tagged = serializers.SerializerMethodField()
    matched_users_count = serializers.SerializerMethodField()
    
    class Meta:
        model = EventPhoto
        fields = ['id', 'event', 'photographer_id', 'photographer_username', 
                 'photographer_email', 'photographer_name', 'image', 'price',
                 'detected_faces', 'uploaded_at', 'is_user_tagged',
                 'matched_users_count']
        read_only_fields = ['detected_faces', 'uploaded_at', 'is_user_tagged', 
                          'matched_users_count']

    def get_photographer_name(self, obj):
        """Get photographer's full name"""
        try:
            if obj.photographer:
                return f"{obj.photographer.first_name} {obj.photographer.last_name}".strip()
            return ""
        except:
            return ""

    def validate_price(self, value):
        event = self.context.get('event')
        if event and event.image_price_limit and value > event.image_price_limit:
            raise serializers.ValidationError(
                f"Price cannot exceed event limit of {event.image_price_limit}"
            )
        return value
    
    def get_is_user_tagged(self, obj):
        """Return false by default since facial recognition runs in background"""
        return False
    
    def get_matched_users_count(self, obj):
        """Return 0 by default since facial recognition runs in background"""
        return 0


class EventSerializer(serializers.ModelSerializer):
    creator = UserSerializer(read_only=True)
    # Use a simpler approach for photographers field to avoid serialization issues
    photographers = serializers.SerializerMethodField()
    subscription = serializers.SerializerMethodField()
    is_attending = serializers.SerializerMethodField()
    attendance_type = serializers.SerializerMethodField()
    distance = serializers.FloatField(read_only=True, required=False)
    is_paid_subscription = serializers.SerializerMethodField()
    event_status = serializers.SerializerMethodField()
    upload_permissions = serializers.SerializerMethodField()
    
    class Meta:
        model = Event
        fields = ['id', 'creator', 'name', 'subscription', 'is_paid_subscription', 'banner_image',
                 'start_date', 'end_date', 'description', 'location',
                 'latitude', 'longitude', 'event_type', 'photographers', 
                 'image_price_limit', 'allow_user_uploads', 'qr_code', 
                 'created_at', 'updated_at', 'total_photos', 'tagged_photos', 
                 'is_attending', 'attendance_type', 'distance', 'visibility',
                 'requires_jersey_number', 'event_status', 'upload_permissions']
        read_only_fields = ['qr_code', 'created_at', 'updated_at', 
                          'total_photos', 'tagged_photos', 'distance', 'event_status', 'upload_permissions']

    def get_photographers(self, obj):
        """Manually serialize photographers to avoid model reference issues"""
        try:
            photographers_data = []
            for photographer in obj.photographers.all():
                photographers_data.append({
                    'id': photographer.id,
                    'username': photographer.username,
                    'first_name': photographer.first_name,
                    'last_name': photographer.last_name,
                    'email': photographer.email
                })
            return photographers_data
        except Exception as e:
            # If there's any issue, return empty list
            return []

    def get_subscription(self, obj):
        if obj.subscription:
            from subscriptions.serializers import SubscriptionSerializer
            return SubscriptionSerializer(obj.subscription).data
        return None
        
    def get_is_paid_subscription(self, obj):
        """Check if the event has a paid subscription"""
        try:
            return obj.is_paid_subscription()
        except:
            return False

    def get_event_status(self, obj):
        """
        Determine the current status of the event based on dates
        Returns: 'scheduled', 'ongoing', 'ongoing_with_buffer', 'completed'
        """
        now = timezone.now().date()
        
        # If event doesn't have start_date, consider it as ongoing
        if not obj.start_date:
            return 'ongoing'
        
        # Event hasn't started yet
        if now < obj.start_date:
            return 'scheduled'
        
        # Event is currently happening (started but not ended, or no end date)
        if not obj.end_date or now <= obj.end_date:
            return 'ongoing'
        
        # Event has ended - check if in buffer period
        if obj.end_date:
            buffer_end_date = obj.end_date + timedelta(days=2)
            
            # Still in buffer period
            if now <= buffer_end_date:
                return 'ongoing_with_buffer'
            else:
                return 'completed'
        
        # Fallback
        return 'ongoing'

    def get_upload_permissions(self, obj):
        """
        Get detailed upload permission information
        """
        now = timezone.now().date()
        
        # Basic upload permission info
        upload_info = {
            'upload_allowed': False,
            'reason': None,
            'days_remaining': None,
            'deadline': None
        }
        
        # If event doesn't have start_date, allow uploads
        if not obj.start_date:
            upload_info['upload_allowed'] = True
            upload_info['reason'] = 'No date restrictions'
            return upload_info
        
        # Event hasn't started yet
        if now < obj.start_date:
            days_until_start = (obj.start_date - now).days
            upload_info['upload_allowed'] = False
            upload_info['reason'] = f'Event starts in {days_until_start} day(s)'
            return upload_info
        
        # Event is ongoing (started but not ended, or no end date)
        if not obj.end_date or now <= obj.end_date:
            upload_info['upload_allowed'] = True
            upload_info['reason'] = 'Event is active'
            return upload_info
        
        # Event has ended - check buffer period
        if obj.end_date:
            buffer_end_date = obj.end_date + timedelta(days=2)
            
            # Still in buffer period
            if now <= buffer_end_date:
                days_remaining = (buffer_end_date - now).days
                upload_info['upload_allowed'] = True
                upload_info['reason'] = f'Buffer period - {days_remaining} day(s) remaining'
                upload_info['days_remaining'] = days_remaining
                upload_info['deadline'] = buffer_end_date.isoformat()
                return upload_info
            else:
                upload_info['upload_allowed'] = False
                upload_info['reason'] = 'Upload deadline has passed'
                upload_info['deadline'] = buffer_end_date.isoformat()
                return upload_info
        
        # Fallback
        upload_info['upload_allowed'] = True
        upload_info['reason'] = 'No restrictions'
        return upload_info

    def validate(self, data):
        if data.get('start_date') and data.get('end_date'):
            if data['start_date'] > data['end_date']:
                raise serializers.ValidationError(
                    "End date must be after or on the same day as start date."
                )
                
        # Check if jersey number is required for sports events
        if data.get('event_type') == Event.EventType.SPORTS:
            data['requires_jersey_number'] = True
            
        return data
    
    def get_is_attending(self, obj):
        """Check if request user is attending this event"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return False
        return EventAttendance.objects.filter(
            event=obj, user=request.user, is_attending=True
        ).exists()
    
    def get_attendance_type(self, obj):
        """Determine if the user created or is attending this event"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return None
            
        if obj.creator == request.user:
            return "Created"
            
        if EventAttendance.objects.filter(
            event=obj, user=request.user, is_attending=True
        ).exists():
            return "Attended"
            
        return None


class EventCreateSerializer(EventSerializer):
    """Serializer for creating events"""
    class Meta(EventSerializer.Meta):
        fields = ['id', 'name', 'subscription', 'banner_image', 'start_date', 'end_date',
                 'description', 'location', 'event_type', 'photographers',
                 'image_price_limit', 'allow_user_uploads', 'visibility',
                 'requires_jersey_number']
        read_only_fields = []


class EventListSerializer(serializers.ModelSerializer):
    """Enhanced serializer for listing events with user relations and status tags"""
    attendance_type = serializers.SerializerMethodField()
    user_relation = serializers.SerializerMethodField()
    event_status = serializers.SerializerMethodField()
    
    class Meta:
        model = Event
        fields = [
            'id', 'name', 'banner_image', 'start_date', 'end_date',
            'event_type', 'total_photos', 'tagged_photos', 
            'attendance_type', 'visibility', 'user_relation', 'event_status'
        ]

    def get_attendance_type(self, obj):
        """Get user's attendance type for this event"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return None
            
        user = request.user
        
        # Check if user is creator
        if obj.creator == user:
            return "Created"
            
        # Check if user is attendee
        try:
            attendance = EventAttendance.objects.get(
                event=obj, 
                user=user, 
                is_attending=True
            )
            return "Attended"
        except EventAttendance.DoesNotExist:
            return None

    def get_user_relation(self, obj):
        """Determine user's relation to the event"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return "not_joined"
            
        user = request.user
        
        # Check if user is creator
        if obj.creator == user:
            return "creator"
            
        # Check if user has joined as attendee
        try:
            EventAttendance.objects.get(
                event=obj, 
                user=user, 
                is_attending=True
            )
            return "joined"
        except EventAttendance.DoesNotExist:
            return "not_joined"

    def get_event_status(self, obj):
        """Calculate event status tag based on user relation and timing"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return "available"
            
        user = request.user
        now = timezone.now()
        one_month_from_now = now + timedelta(days=30)
        
        # Determine if user has joined the event
        user_joined = False
        if obj.creator == user:
            user_joined = True
        else:
            try:
                EventAttendance.objects.get(
                    event=obj, 
                    user=user, 
                    is_attending=True
                )
                user_joined = True
            except EventAttendance.DoesNotExist:
                user_joined = False
        
        # Convert dates to datetime objects for comparison if needed
        start_date = obj.start_date
        end_date = obj.end_date
        
        # Handle case where start_date might be a date object
        if hasattr(start_date, 'date'):
            # It's already a datetime object
            pass
        else:
            # It's a date object, convert to datetime for comparison
            start_date = timezone.datetime.combine(start_date, timezone.datetime.min.time())
            start_date = timezone.make_aware(start_date) if timezone.is_naive(start_date) else start_date
            
        if end_date and hasattr(end_date, 'date'):
            # It's already a datetime object
            pass
        elif end_date:
            # It's a date object, convert to datetime for comparison
            end_date = timezone.datetime.combine(end_date, timezone.datetime.max.time())
            end_date = timezone.make_aware(end_date) if timezone.is_naive(end_date) else end_date
        
        # Logic for status tags
        if user_joined:
            # User has joined the event
            if end_date and end_date < now:
                # Event has ended
                return "completed"
            elif start_date <= now and (not end_date or end_date >= now):
                # Event is currently ongoing
                return "ongoing"
            elif start_date > now:
                # Event is scheduled for future
                return "scheduled"
            else:
                # Fallback for edge cases
                return "scheduled"
        else:
            # User has not joined the event
            # Check if event is within next month or ongoing
            if start_date <= one_month_from_now:
                # Event is within next month or ongoing
                if end_date and end_date < now:
                    # Event has ended - shouldn't show this event in filtered list
                    return "expired"
                else:
                    return "available"
            else:
                # Event is more than a month away
                return "future"


class EventAttendanceSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    event = EventSerializer(read_only=True)
    
    class Meta:
        model = EventAttendance
        fields = ['id', 'event', 'user', 'joined_at', 
                 'is_attending', 'attendance_method', 'jersey_number']
        read_only_fields = ['joined_at']


class EventJoinSerializer(serializers.Serializer):
    event_id = serializers.UUIDField(required=True)
    attendance_method = serializers.ChoiceField(
        choices=['QR_CODE', 'INVITE', 'SELF_JOIN'],
        default='SELF_JOIN'
    )
    jersey_number = serializers.CharField(required=False, allow_blank=True)


class NearbyEventsRequestSerializer(serializers.Serializer):
    latitude = serializers.FloatField(required=True)
    longitude = serializers.FloatField(required=True)
    radius = serializers.FloatField(required=False, default=10)  # km
    
    
class EventSearchSerializer(serializers.Serializer):
    """Serializer for searching events"""
    query = serializers.CharField(required=False, allow_blank=True)
    event_type = serializers.ChoiceField(
        choices=Event.EventType.choices, 
        required=False,
        allow_blank=True
    )
    location = serializers.CharField(required=False, allow_blank=True)
    date = serializers.DateField(required=False)


class EventPhotoUploadSerializer(serializers.Serializer):
    """Serializer for uploading photos to an event"""
    image = serializers.ImageField(required=True)
    price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True)


class BatchEventPhotoUploadSerializer(serializers.Serializer):

    """Serializer for batch uploading photos to an event"""
    images = serializers.ListField(
        child=serializers.ImageField(required=True),
        required=True
    )
    prices = serializers.ListField(
        child=serializers.DecimalField(max_digits=10, decimal_places=2, required=False, allow_null=True),
        required=False
    )


class PhotographerEventListSerializer(serializers.ModelSerializer):
    """Enhanced serializer for photographer's assigned events with status tags"""
    attendance_type = serializers.SerializerMethodField()
    user_relation = serializers.SerializerMethodField()
    event_status = serializers.SerializerMethodField()
    
    class Meta:
        model = Event
        fields = [
            'id', 'name', 'banner_image', 'start_date', 'end_date',
            'event_type', 'total_photos', 'tagged_photos', 
            'attendance_type', 'visibility', 'user_relation', 'event_status'
        ]

    def get_attendance_type(self, obj):
        """Get user's attendance type for this event with photographer support
        
        Returns:
        - "Created": User created the event
        - "Photographer": Approved photographer
        - "Invited Photographer": Photographer invited but not yet joined
        - "Pending Photographer": Photographer requested, awaiting approval
        - "Attended": User joined as attendee
        - None: No relation to event
        """
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return None
            
        user = request.user
        
        # Check if user is creator
        if obj.creator == user:
            return "Created"
        
        # *** NEW: Check if user is photographer with invitation status ***
        try:
            photographer_role = EventUserRole.objects.get(
                event=obj,
                user=user,
                role=EventUserRole.RoleChoices.PHOTOGRAPHER,
                is_active=True
            )
            
            if photographer_role.invitation_status == EventUserRole.InvitationStatus.APPROVED:
                return "Photographer"
            elif photographer_role.invitation_status == EventUserRole.InvitationStatus.INVITED:
                return "Invited Photographer"
            elif photographer_role.invitation_status == EventUserRole.InvitationStatus.PENDING_REQUEST:
                return "Pending Photographer"
            else:
                return "Photographer"  # Fallback
                
        except EventUserRole.DoesNotExist:
            pass
            
        # Check if user is attendee
        try:
            attendance = EventAttendance.objects.get(
                event=obj, 
                user=user, 
                is_attending=True
            )
            return "Attended"
        except EventAttendance.DoesNotExist:
            return None

    def get_user_relation(self, obj):
        """Determine user's relation to the event with invitation system support
        
        Returns:
        - "creator": User created the event
        - "invited": Photographer was invited by organizer
        - "pending_request": Photographer requested to join, awaiting approval
        - "photographer": Approved photographer
        - "denied": Photographer request was denied
        - "joined": User joined as attendee
        - "not_joined": No relation to event
        """
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return "not_joined"
            
        user = request.user
        
        # Check if user is creator
        if obj.creator == user:
            return "creator"
        
        # *** NEW: Check for photographer invitation status ***
        try:
            photographer_role = EventUserRole.objects.get(
                event=obj,
                user=user,
                role=EventUserRole.RoleChoices.PHOTOGRAPHER,
                is_active=True
            )
            
            # Return status based on invitation_status
            if photographer_role.invitation_status == EventUserRole.InvitationStatus.INVITED:
                return "invited"
            elif photographer_role.invitation_status == EventUserRole.InvitationStatus.PENDING_REQUEST:
                return "pending_request"
            elif photographer_role.invitation_status == EventUserRole.InvitationStatus.APPROVED:
                return "photographer"
            elif photographer_role.invitation_status == EventUserRole.InvitationStatus.DENIED:
                return "denied"
            elif photographer_role.invitation_status == EventUserRole.InvitationStatus.CANCELLED:
                return "not_joined"
            else:
                return "photographer"  # Default fallback for approved photographers
                
        except EventUserRole.DoesNotExist:
            pass
        
        # *** NEW: Check for photographer invitation by email (when user hasn't joined yet) ***
        try:
            email_invitation = EventUserRole.objects.get(
                event=obj,
                invitation_email=user.email,
                role=EventUserRole.RoleChoices.PHOTOGRAPHER,
                user__isnull=True,  # No user assigned yet
                invitation_status=EventUserRole.InvitationStatus.INVITED
            )
            return "invited"
        except EventUserRole.DoesNotExist:
            pass
            
        # Check if user has joined as attendee
        try:
            EventAttendance.objects.get(
                event=obj, 
                user=user, 
                is_attending=True
            )
            return "joined"
        except EventAttendance.DoesNotExist:
            return "not_joined"

    def get_event_status(self, obj):
        """Calculate event status tag based on photographer relation and timing"""
        request = self.context.get('request')
        if not request or not request.user.is_authenticated:
            return "available"
            
        user = request.user
        now = timezone.now().date()  # Convert to date for comparison
        one_month_from_now = now + timedelta(days=30)
        
        # Determine if user is assigned to this event as photographer or creator
        user_assigned = False
        if obj.creator == user:
            user_assigned = True
        elif user in obj.photographers.all():
            user_assigned = True
        
        # Convert event dates to date objects for comparison
        start_date = obj.start_date.date() if hasattr(obj.start_date, 'date') else obj.start_date
        end_date = obj.end_date.date() if obj.end_date and hasattr(obj.end_date, 'date') else obj.end_date
        
        # Logic for status tags (photographer perspective)
        if user_assigned:
            # User is assigned as photographer or is creator
            if end_date and end_date < now:
                # Event has ended
                return "completed"
            elif start_date <= now and (not end_date or end_date >= now):
                # Event is currently ongoing
                return "ongoing"
            elif start_date > now:
                # Event is scheduled for future
                return "scheduled"
            else:
                # Fallback for edge cases
                return "scheduled"
        else:
            # User is not assigned as photographer
            # Check if event is within next month or ongoing (available for assignment)
            if start_date <= one_month_from_now:
                # Event is within next month or ongoing
                if end_date and end_date < now:
                    # Event has ended - shouldn't show this event in filtered list
                    return "expired"
                else:
                    return "available"
            else:
                # Event is more than a month away
                return "future"
            
            
class PhotographerRequestSerializer(serializers.ModelSerializer):
    """Serializer for photographer requests"""
    photographer_name = serializers.CharField(source='user.get_full_name', read_only=True)
    photographer_email = serializers.CharField(source='user.email', read_only=True)
    event_name = serializers.CharField(source='event.name', read_only=True)
    requested_date = serializers.DateTimeField(source='created_at', read_only=True)
    
    class Meta:
        model = EventUserRole
        fields = [
            'id', 'photographer_name', 'photographer_email', 'event_name',
            'request_message', 'requested_date', 'invitation_status',
            'denial_count', 'specialization', 'portfolio_link'
        ]
        read_only_fields = ['id', 'photographer_name', 'photographer_email', 'event_name', 'requested_date']


# *** NEW SERIALIZER ADDED: For join as photographer requests ***
class JoinAsPhotographerSerializer(serializers.Serializer):
    """Serializer for photographer join requests"""
    request_message = serializers.CharField(
        max_length=500, 
        required=False, 
        help_text="Optional message to event organizer"
    )
    portfolio_link = serializers.URLField(
        required=False, 
        help_text="Link to your photography portfolio"
    )
    specialization = serializers.CharField(
        max_length=100, 
        required=False,
        help_text="Your photography specialization (e.g., wedding, sports, portrait)"
    )
