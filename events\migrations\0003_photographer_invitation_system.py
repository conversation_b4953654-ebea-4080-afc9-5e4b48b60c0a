# events/migrations/0003_photographer_invitation_system.py
# Generated for PhotoFish 2.0 Photographer Invitation System

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('events', '0002_initial'),  # Replace with your latest migration
    ]

    operations = [
        # Add new fields to EventUserRole model
        migrations.AddField(
            model_name='eventuserrole',
            name='invitation_status',
            field=models.CharField(
                choices=[
                    ('INVITED', 'Invited by Organizer'), 
                    ('PENDING_REQUEST', 'Pending Approval'), 
                    ('APPROVED', 'Approved'), 
                    ('DENIED', 'Denied'), 
                    ('CANCELLED', 'Cancelled')
                ], 
                default='APPROVED', 
                max_length=20
            ),
        ),
        migrations.AddField(
            model_name='eventuserrole',
            name='invited_by',
            field=models.ForeignKey(
                blank=True, 
                null=True, 
                on_delete=django.db.models.deletion.SET_NULL, 
                related_name='photographer_invitations', 
                to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name='eventuserrole',
            name='invitation_email',
            field=models.EmailField(blank=True, help_text='Email used for invitation', max_length=254),
        ),
        migrations.AddField(
            model_name='eventuserrole',
            name='request_message',
            field=models.TextField(blank=True, help_text="Photographer's application message"),
        ),
        migrations.AddField(
            model_name='eventuserrole',
            name='approval_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='eventuserrole',
            name='denial_count',
            field=models.IntegerField(default=0, help_text='Track denials for 3-strike rule'),
        ),
        
        # Add new index for invitation_status field
        migrations.AddIndex(
            model_name='eventuserrole',
            index=models.Index(fields=['invitation_status'], name='events_eventuserrole_invitation_status_idx'),
        ),
        
        # Update Notification model notification_type choices
        migrations.AlterField(
            model_name='notification',
            name='notification_type',
            field=models.CharField(
                choices=[
                    ('payment_received', 'Payment Received'), 
                    ('payment_pending', 'Payment Pending'), 
                    ('payment_disbursed', 'Payment Disbursed'), 
                    ('event_accepted', 'Event Accepted'), 
                    ('event_invitation', 'Event Invitation'), 
                    ('photo_processed', 'Photo Processed'),
                    ('photographer_request', 'Photographer Request'),
                    ('photographer_joined', 'Photographer Joined'),
                    ('request_approved', 'Request Approved'),
                    ('request_denied', 'Request Denied'),
                ], 
                max_length=50
            ),
        ),
    ]