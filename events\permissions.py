from rest_framework import permissions
from rest_framework import permissions
from .models import EventUserRole

class IsEventOwnerOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow owners of an event to edit it.
    """
    
    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed to any request,
        # so we'll always allow GET, HEAD or OPTIONS requests.
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write permissions are only allowed to the event creator
        return obj.creator == request.user


class IsEventCreatorOrPhotographer(permissions.BasePermission):
    """
    Custom permission to allow only event creator or APPROVED photographers
    to perform actions like uploading photos.
    
    IMPORTANT: Only APPROVED photographers can upload photos.
    INVITED and PENDING_REQUEST photographers are blocked.
    """
    
    def has_object_permission(self, request, view, obj):
        # Check if user is the creator
        if obj.creator == request.user:
            return True
        
        # *** UPDATED: Check if user is an APPROVED photographer ***
        try:
            photographer_role = EventUserRole.objects.get(
                user=request.user,
                event=obj,
                role=EventUserRole.RoleChoices.PHOTOGRAPHER,
                is_active=True
            )
            
            # *** NEW: Only APPROVED photographers can upload photos ***
            if photographer_role.invitation_status == EventUserRole.InvitationStatus.APPROVED:
                return True
            
            # Block INVITED, PENDING_REQUEST, DENIED, CANCELLED photographers
            return False
            
        except EventUserRole.DoesNotExist:
            return False
   
    
class CanJoinLeaveEvent(permissions.BasePermission):
    """Allow any authenticated user to join or leave an event."""
    
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated
        
    def has_object_permission(self, request, view, obj):
        # For join/leave actions, any authenticated user can perform
        if view.action in ['join_event', 'leave_event']:
            return True
        # For other actions, defer to the default permission checks
        return False    
    
class CanManageOwnPhotos(permissions.BasePermission):
    """
    Allow only APPROVED photographers to manage their own uploaded photos.
    Event creators can manage all photos.
    """
    
    def has_object_permission(self, request, view, obj):
        # Event creators can manage all photos
        if obj.event.creator == request.user:
            return True
        
        # *** UPDATED: Check if user is APPROVED photographer for this event ***
        if hasattr(obj, 'uploaded_by') and obj.uploaded_by == request.user:
            try:
                photographer_role = EventUserRole.objects.get(
                    user=request.user,
                    event=obj.event,
                    role=EventUserRole.RoleChoices.PHOTOGRAPHER,
                    is_active=True
                )
                
                # *** NEW: Only APPROVED photographers can manage photos ***
                return photographer_role.invitation_status == EventUserRole.InvitationStatus.APPROVED
                
            except EventUserRole.DoesNotExist:
                return False
        
        return False

# *** NEW PERMISSION CLASS ADDED ***
class CanManagePhotographerRequests(permissions.BasePermission):
    """
    Permission for managing photographer requests.
    Only event creators can approve/deny photographer requests.
    """
    
    def has_object_permission(self, request, view, obj):
        # Only event creators can manage photographer requests
        return obj.creator == request.user


# *** NEW PERMISSION CLASS ADDED ***
class CanJoinAsPhotographer(permissions.BasePermission):
    """
    Permission for joining events as photographer.
    Any authenticated user except event creator can request to join as photographer.
    """
    
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        # Event creators cannot join their own events as photographers
        if obj.creator == request.user:
            return False
        
        # Any other authenticated user can request to join
        return True

class IsApprovedPhotographerOrCreator(permissions.BasePermission):
    """
    Permission that allows only event creators and APPROVED photographers.
    Used for photo-related operations.
    """
    
    def has_object_permission(self, request, view, obj):
        # Event creators have full access
        if obj.creator == request.user:
            return True
        
        # Check if user is APPROVED photographer
        try:
            photographer_role = EventUserRole.objects.get(
                user=request.user,
                event=obj,
                role=EventUserRole.RoleChoices.PHOTOGRAPHER,
                is_active=True
            )
            
            # Only APPROVED photographers allowed
            return photographer_role.invitation_status == EventUserRole.InvitationStatus.APPROVED
            
        except EventUserRole.DoesNotExist:
            return False