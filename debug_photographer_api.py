# debug_photographer_api.py
# Save this file in your project root and run: python manage.py shell < debug_photographer_api.py

print("🔍 DEBUGGING PHOTOGRAPHER REQUESTS API")
print("=" * 60)

# Test 1: Check if URL routing works
print("\n1️⃣ TESTING URL ROUTING")
try:
    from django.urls import reverse
    url = reverse('event-my-photographer-requests')
    print(f"✅ URL found: {url}")
except Exception as e:
    print(f"❌ URL error: {e}")

# Test 2: Check if EventViewSet and method exist
print("\n2️⃣ TESTING VIEWSET AND METHOD")
try:
    from events.views import EventViewSet
    viewset = EventViewSet()
    
    # Check if method exists
    has_method = hasattr(viewset, 'my_photographer_requests')
    print(f"Method exists: {'✅' if has_method else '❌'} {has_method}")
    
    if has_method:
        method = getattr(viewset, 'my_photographer_requests')
        print(f"Method type: {type(method)}")
        print(f"Method callable: {'✅' if callable(method) else '❌'} {callable(method)}")
        
        # Check if it has the action decorator
        if hasattr(method, 'mapping'):
            print(f"✅ Has action mapping: {method.mapping}")
        else:
            print("❌ No action mapping found")
            
        if hasattr(method, 'detail'):
            print(f"✅ Detail setting: {method.detail}")
        else:
            print("❌ No detail setting found")
    
except Exception as e:
    print(f"❌ ViewSet import error: {e}")

# Test 3: Check EventUserRole model and data
print("\n3️⃣ TESTING MODEL AND DATA")
try:
    from events.models import EventUserRole
    from django.contrib.auth import get_user_model
    
    User = get_user_model()
    
    # Count total EventUserRole records
    total_roles = EventUserRole.objects.count()
    print(f"Total EventUserRole records: {total_roles}")
    
    # Count photographer roles
    photographer_roles = EventUserRole.objects.filter(
        role=EventUserRole.RoleChoices.PHOTOGRAPHER
    ).count()
    print(f"Photographer roles: {photographer_roles}")
    
    # <NAME_EMAIL> (from your admin)
    curry_user = User.objects.filter(email='<EMAIL>').first()
    if curry_user:
        print(f"✅ Found <EMAIL> user: {curry_user.id}")
        
        curry_roles = EventUserRole.objects.filter(
            user=curry_user,
            role=EventUserRole.RoleChoices.PHOTOGRAPHER
        )
        print(f"Curry's photographer roles: {curry_roles.count()}")
        
        for role in curry_roles:
            print(f"  - {role.event.name}: {role.invitation_status} (ID: {role.id})")
    else:
        print("❌ <EMAIL> user not found")
        
except Exception as e:
    print(f"❌ Model error: {e}")

# Test 4: Test the method directly
print("\n4️⃣ TESTING METHOD DIRECTLY")
try:
    from events.views import EventViewSet
    from django.test import RequestFactory
    from django.contrib.auth import get_user_model
    from rest_framework.test import force_authenticate
    
    User = get_user_model()
    
    # Get curry user for testing
    test_user = User.objects.filter(email='<EMAIL>').first()
    if not test_user:
        test_user = User.objects.first()
    
    if test_user:
        print(f"Testing with user: {test_user.email}")
        
        # Create mock request
        factory = RequestFactory()
        request = factory.get('/api/v1/events/my-photographer-requests/')
        request.user = test_user
        
        # Test the method
        viewset = EventViewSet()
        viewset.request = request
        
        try:
            response = viewset.my_photographer_requests(request)
            print(f"✅ Method executed successfully!")
            print(f"Response status: {response.status_code}")
            print(f"Response type: {type(response)}")
            
            if hasattr(response, 'data'):
                data = response.data
                print(f"Response data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                
                if isinstance(data, dict):
                    print(f"Success: {data.get('success')}")
                    print(f"Total count: {data.get('total_count')}")
                    print(f"Summary: {data.get('summary')}")
                    
                    requests = data.get('requests', [])
                    if requests:
                        print(f"First request ID: {requests[0].get('id')}")
                        print(f"First request event: {requests[0].get('event_name')}")
                    else:
                        print("No requests in response")
            
        except Exception as method_error:
            print(f"❌ Method execution error: {method_error}")
            import traceback
            traceback.print_exc()
    else:
        print("❌ No users found for testing")
        
except Exception as e:
    print(f"❌ Direct method test error: {e}")

# Test 5: Check imports and decorators
print("\n5️⃣ TESTING IMPORTS AND DECORATORS")
try:
    from rest_framework.decorators import action
    from rest_framework.response import Response
    from rest_framework import viewsets
    print("✅ All required imports available")
    
    # Check the actual decorator on the method
    from events.views import EventViewSet
    method = getattr(EventViewSet, 'my_photographer_requests', None)
    
    if method:
        # Check if it's properly decorated
        if hasattr(method, 'mapping'):
            print(f"✅ Method has action mapping: {method.mapping}")
        if hasattr(method, 'detail'):
            print(f"✅ Method detail setting: {method.detail}")
        if hasattr(method, 'url_path'):
            print(f"✅ Method URL path: {method.url_path}")
        if hasattr(method, 'url_name'):
            print(f"✅ Method URL name: {method.url_name}")
            
except Exception as e:
    print(f"❌ Import/decorator error: {e}")

# Test 6: Test router registration
print("\n6️⃣ TESTING ROUTER REGISTRATION")
try:
    from events.urls import router
    
    print(f"Router class: {type(router)}")
    print(f"Registered viewsets: {len(router.registry)}")
    
    for prefix, viewset, basename in router.registry:
        print(f"  - {prefix} -> {viewset} (basename: {basename})")
        
        if viewset == EventViewSet:
            print("  ✅ EventViewSet is registered")
            
            # Check the viewset's actions
            if hasattr(viewset, 'my_photographer_requests'):
                print("  ✅ my_photographer_requests method found in registered viewset")
            else:
                print("  ❌ my_photographer_requests method NOT found in registered viewset")
    
except Exception as e:
    print(f"❌ Router registration error: {e}")

# Test 7: Test actual HTTP request simulation
print("\n7️⃣ TESTING HTTP REQUEST SIMULATION")
try:
    from django.test import Client
    from django.contrib.auth import get_user_model
    
    User = get_user_model()
    client = Client()
    
    # Get test user
    test_user = User.objects.filter(email='<EMAIL>').first()
    if not test_user:
        test_user = User.objects.first()
    
    if test_user:
        # Force login
        client.force_login(test_user)
        
        # Test the actual URL
        response = client.get('/api/v1/events/my-photographer-requests/')
        
        print(f"HTTP Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ HTTP request successful!")
            try:
                import json
                data = json.loads(response.content)
                print(f"Response data: {data}")
            except:
                print(f"Response content: {response.content}")
        elif response.status_code == 404:
            print("❌ 404 - URL not found")
        elif response.status_code == 403:
            print("❌ 403 - Permission denied")
        elif response.status_code == 401:
            print("❌ 401 - Authentication required")
        else:
            print(f"❌ Unexpected status: {response.status_code}")
            print(f"Response content: {response.content}")
    
except Exception as e:
    print(f"❌ HTTP simulation error: {e}")

# Test 8: Check URL patterns
print("\n8️⃣ TESTING URL PATTERNS")
try:
    from django.urls import get_resolver
    from django.conf import settings
    
    resolver = get_resolver()
    
    def find_photographer_urls(url_patterns, prefix=''):
        photographer_urls = []
        for pattern in url_patterns:
            if hasattr(pattern, 'url_patterns'):
                # Include pattern
                photographer_urls.extend(
                    find_photographer_urls(pattern.url_patterns, prefix + str(pattern.pattern))
                )
            else:
                # URL pattern
                pattern_str = prefix + str(pattern.pattern)
                if 'photographer' in pattern_str.lower():
                    photographer_urls.append(pattern_str)
        return photographer_urls
    
    photographer_urls = find_photographer_urls(resolver.url_patterns)
    
    print("URLs containing 'photographer':")
    for url in photographer_urls:
        print(f"  - {url}")
    
except Exception as e:
    print(f"❌ URL patterns error: {e}")

print("\n" + "=" * 60)
print("🏁 DEBUG COMPLETE")
print("\nSUMMARY:")
print("- If URL is found but method fails, there's a method issue")
print("- If HTTP request fails with 404, there's a routing issue")  
print("- If method works directly but HTTP fails, there's a middleware/permission issue")
print("- Check the specific error messages above for the root cause")